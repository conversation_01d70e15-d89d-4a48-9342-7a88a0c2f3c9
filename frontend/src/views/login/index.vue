<template>
  <AppPage :show-footer="true" bg-cover :style="{ backgroundImage: `url(${bgImg})` }">
    <div
      style="transform: translateY(25px)"
      class="m-auto max-w-1500 min-w-345 f-c-c rounded-10 bg-white bg-opacity-60 p-15 card-shadow"
      dark:bg-dark
    >
      <div hidden w-380 px-20 py-35 md:block>
        <icon-custom-front-page pt-10 text-300 color-primary></icon-custom-front-page>
      </div>

      <div w-320 flex-col px-20 py-35>
        <h5 f-c-c text-24 font-normal color="#6a6a6a">
          <icon-custom-logo mr-10 text-50 color-primary />{{ $t('app_name') }}
        </h5>
        <div mt-30>
          <n-input
            v-model:value="loginInfo.username"
            autofocus
            class="h-50 items-center pl-10 text-16"
            placeholder="请输入用户名或手机号"
            :maxlength="20"
          />
        </div>
        <div mt-30>
          <n-input
            v-model:value="loginInfo.password"
            class="h-50 items-center pl-10 text-16"
            type="password"
            show-password-on="mousedown"
            placeholder="请输入密码"
            :maxlength="20"
            @keypress.enter="handleLogin"
          />
        </div>

        <!-- 验证码输入框 -->
        <div mt-30 flex items-center gap-10>
          <n-input
            v-model:value="loginInfo.captcha"
            class="h-50 flex-1 items-center pl-10 text-16"
            placeholder="请输入验证码"
            :maxlength="6"
            @keypress.enter="handleLogin"
          />
          <div
            class="h-50 w-120 flex cursor-pointer items-center justify-center border border-gray-300 rounded bg-gray-50"
            @click="refreshCaptcha"
          >
            <img
              v-if="captchaImage"
              :src="captchaImage"
              alt="验证码"
              class="h-full w-full object-contain"
            />
            <span v-else class="text-12 text-gray-400">点击获取</span>
          </div>
        </div>

        <div mt-20>
          <n-button
            h-50
            w-full
            rounded-5
            text-16
            type="primary"
            :loading="loading"
            @click="handleLogin"
          >
            {{ $t('views.login.text_login') }}
          </n-button>
        </div>
      </div>
    </div>
  </AppPage>
</template>

<script setup>
import { lStorage, setToken } from '@/utils'
import bgImg from '@/assets/images/login_bg.webp'
import api from '@/api'
import { addDynamicRoutes } from '@/router'
import { useI18n } from 'vue-i18n'

const router = useRouter()
const { query } = useRoute()
const { t } = useI18n({ useScope: 'global' })

const loginInfo = ref({
  username: '',
  password: '',
  captcha: '',
})

// 验证码相关
const captchaImage = ref('')
const captchaKey = ref('')

initLoginInfo()

function initLoginInfo() {
  const localLoginInfo = lStorage.get('loginInfo')
  if (localLoginInfo) {
    loginInfo.value.username = localLoginInfo.username || ''
    loginInfo.value.password = localLoginInfo.password || ''
  }
}

// 获取验证码
async function getCaptcha() {
  try {
    const res = await api.captcha()
    if (res.data) {
      captchaImage.value = res.data.b64_image // 假设后端返回base64图片
      captchaKey.value = res.data.key // 验证码的唯一标识
    }
  } catch (error) {
    console.error('获取验证码失败:', error)
    $message.error('获取验证码失败')
  }
}

// 刷新验证码
function refreshCaptcha() {
  loginInfo.value.captcha = ''
  getCaptcha()
}

const loading = ref(false)
async function handleLogin() {
  const { username, password, captcha } = loginInfo.value
  if (!username || !password) {
    $message.warning(t('views.login.message_input_username_password'))
    return
  }
  if (!captcha) {
    $message.warning('请输入验证码')
    return
  }
  try {
    loading.value = true
    $message.loading(t('views.login.message_verifying'))
    const res = await api.login({
      username,
      password: password.toString(),
      captcha,
      captcha_key: captchaKey.value,
    })
    $message.success(t('views.login.message_login_success'))
    setToken(res.data.access_token)
    await addDynamicRoutes()
    if (query.redirect) {
      const path = query.redirect
      console.log('path', { path, query })
      Reflect.deleteProperty(query, 'redirect')
      router.push({ path, query })
    } else {
      router.push('/')
    }
  } catch (e) {
    console.error('login error', e.error)
    // 登录失败后刷新验证码
    refreshCaptcha()
  }
  loading.value = false
}

// 组件挂载时获取验证码
onMounted(() => {
  getCaptcha()
})
</script>
