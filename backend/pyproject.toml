[[tool.uv.index]]
url = "https://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple"
default = true

[project]
name = "msf-person-admin"
version = "0.1.0"
description = "眉山房产网个人信息管理端"
authors = [
    {name = "thoughtsdump", email = "<EMAIL>"},
]
requires-python = ">=3.11"
dependencies = [
    "aerich>=0.9.1",
    "asyncpg>=0.30.0",
    "email-validator>=2.2.0",
    "fastapi>=0.116.1",
    "loguru>=0.7.3",
    "odoo-rpc-client>=1.2.0",
    "passlib>=1.7.4",
    "pillow>=11.3.0",
    "psutil>=7.0.0",
    "pycryptodome>=3.23.0",
    "pydantic>=2.11.7",
    "pydantic-settings>=2.10.1",
    "python-jose>=3.5.0",
    "python-multipart>=0.0.20",
    "pyyaml>=6.0.2",
    "redis>=6.4.0",
    "ruff>=0.12.10",
    "shortuuid>=1.0.13",
    "tortoise-orm>=0.25.1",
    "user-agents>=2.2.0",
    "uvicorn>=0.35.0",
]


[tool.ruff]
# 最大行长度（默认 88，可改为 100/120）
line-length = 120

target-version = "py311"
# 适用于哪些文件
include = ["**/*.py", "**/*.pyi"]
exclude = [
    ".git",
    ".mypy_cache",
    ".ruff_cache",
    "__pycache__",
    "build",
    "dist",
    "venv",
    ".venv",
]

[tool.ruff.lint]
# lint相关配置
select = [
    "E", # pycodestyle errors
    "W", # pycodestyle warnings
    "F", # pyflakes
    "I", # isort
    "C", # flake8-comprehensions
    "B", # flake8-bugbear
    "Q", # flake8-quotes
]
extend-ignore = [
    "E501", # line too long
    #    "W293",   # blank line contains whitespace
    "B008", #ruff规则禁止在函数参数默认值中直接调用， FastAPI中禁用这个规则
]
fixable = ["ALL"]
unfixable = []


# Python 模块导入排序（等同于 isort）
[tool.ruff.lint.isort]
known-first-party = ["msf-person-admin"]
force-sort-within-sections = true
lines-after-imports = 2
combine-as-imports = true

# 命名规范（PEP8-Naming）
[tool.ruff.lint.pep8-naming]
classmethod-decorators = ["classmethod"]
staticmethod-decorators = ["staticmethod"]

# 格式化选项（ruff formatter，代替 black）
[tool.ruff.format]
quote-style = "single"   # 可选：double 或 single
indent-style = "space"   # 可选：space 或 tab
line-ending = "lf"
skip-magic-trailing-comma = false # # 控制逗号后的空格

[tool.ruff.lint.flake8-quotes]
inline-quotes = "single"
multiline-quotes = "double"
docstring-quotes = "double"

# 类型注解规范（ANN）
[tool.ruff.lint.flake8-annotations]
mypy-init-return = true
suppress-none-returning = true
suppress-dummy-args = true
allow-star-arg-any = true



[tool.aerich]
tortoise_orm = "app.core.TORTOISE_ORM"
#tortoise_orm = "app.core.config.AppSettings.TORTOISE_ORM"
location = "./migrations"
src_folder = "./."
