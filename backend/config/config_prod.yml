# =================================================
# ******************* APP基础配置 ******************
# =================================================
# 开发环境模式配置
APP_ENV: prod
# 是否启用自动重载
APP_RELOAD: true
# 主机IP
APP_SERVER_HOST: 0.0.0.0
# 主机端口
APP_SERVER_PORT: 80
# API前缀
APP_API_PREFIX: ""
# 是否允许一个账号同时登录
APP_SAME_TIME_LOGIN: true

# =================================================
# ******************* API文档配置 ******************
# =================================================
# 应用名称
APP_NAME: MsfPbaAdmin
# 应用描述
APP_DESCRIPTION: MsfPbaAdminAPI
# 应用版本号
APP_VERSION: "1.0"
# 文档访问路径
APP_DOCS_URL: /docs
# OpenAPI JSON路径
APP_OPENAPI_URL: /openapi.json
# Redoc文档路径
APP_REDOC_URL: /redoc

# =================================================
# ***************** 静态文件目录配置 ****************
# =================================================
# 是否启用静态文件服务
APP_STATIC_ENABLE: true
# 静态文件访问URL前缀
APP_STATIC_URL: /static
# 静态文件目录名称
APP_STATIC_DIR: static

# =================================================
# ********************* 日志配置 *******************
# =================================================
# 是否记录审计日志
APP_AUDITLOG_RECORD: true
# 需要记录审计日志的HTTP方法
APP_AUDITLOG_RECORD_METHOD:
  - GET
  - POST
  - PUT
  - DELETE
  - PATCH
# 不记录审计日志的路径
APP_AUDITLOG_RECORD_EXCLUDE:
  - /api/v1/base/access_token
  - /docs
  - /openapi.json

# =================================================
# ******************** 跨域配置 ********************
# =================================================
# 是否启用跨域支持
APP_CORS_ORIGIN_ENABLE: true
# 允许跨域的来源列表
APP_ALLOW_ORIGINS:
  - "*"
# 允许的HTTP方法
APP_ALLOW_METHODS:
  - "*"
# 允许的请求头
APP_ALLOW_HEADERS:
  - "*"
# 是否支持发送Cookie
APP_ALLOW_CREDENTIALS: true

# =================================================
# ******************* 登录认证配置 ******************
# =================================================
# JWT密钥
JWT_SECRET_KEY: dYDU5Lcp93KkFMDgPMYO9UYGnyPvLX0Cyv8tS2WmMWy08UVfnqcjcvJP5GM0LWb4
# JWT加密算法
JWT_ALGORITHM: HS256
# 访问令牌过期时间（分钟）
JWT_ACCESS_TOKEN_EXPIRE_MINUTES: 1440
# 刷新令牌过期时间（分钟）
JWT_REFRESH_TOKEN_EXPIRE_MINUTES: 10080
## Redis中JWT的过期时间（分钟）
#JWT_REDIS_EXPIRE_MINUTES: 30

# 用户默认密码
DEFAULT_PASSWORD: 123456

# =================================================
# ******************** 验证码配置 *******************
# =================================================
# 是否启用验证码
CAPTCHA_ENABLE: true
# 是否启用验证码小写验证
CAPTCHA_LOWER_ENABLE: true
# 验证码过期时间（秒）
CAPTCHA_EXPIRE_SECONDS: 60

# =================================================
# ******************** 数据库配置 *******************
# =================================================
# Tortoise ORM配置
TORTOISE_ORM:
  connections:
    #    sqlite:
    #      # 数据库引擎
    #      engine: tortoise.backends.sqlite
    #      credentials:
    #        # SQLite数据库文件路径
    #        file_path: "./db.sqlite3"

    postgres:
      # 数据库引擎
      engine: tortoise.backends.asyncpg
      credentials:
        host: db
        port: 5432
        user: odoo
        password: jYMyB16KJOL2eNKG
        database: msf_pba
  apps:
    models:
      models:
        - app.models
        - aerich.models
      # 默认数据库连接
      default_connection: postgres
  # 是否使用时区
  use_tz: false
  # 时区设置
  timezone: Asia/Shanghai

# =================================================
# ******************** Redis配置 *******************
# =================================================
# 是否启用Redis
REDIS_ENABLE: true
# Redis主机地址
REDIS_HOST: *************
# Redis端口
REDIS_PORT: 6379
# Redis用户名
REDIS_USERNAME: default
# Redis密码
REDIS_PASSWORD: seF2AvrY9D7Q
# Redis数据库索引
REDIS_DATABASE: 2

# =================================================
# ***************** 文件上传配置 ********************
# =================================================
# 文件上传路径
UPLOAD_PATH: /static/upload
# 文件上传大小限制（字节）
UPLOAD_SIZE: 5242880
# 允许上传的文件扩展名列表
DEFAULT_ALLOWED_EXTENSION:
  - bmp
  - gif
  - jpg
  - jpeg
  - png
  - doc
  - docx
  - xls
  - xlsx
  - ppt
  - pptx
  - html
  - htm
  - txt
  - rar
  - zip
  - gz
  - bz2
  - mp4
  - avi
  - rmvb
  - pdf


# ================================================= #
# ******************** 验证码配置 ******************* #
# ================================================= #

SMS_HOST: *************:8069
SMS_URL: http://*************:8069
SMS_SEND_URL: http://*************:8069/msf_sms/send_verification_code
SMS_VERIFY_URL: http://*************:8069/msf_sms/sms_code_verify
SMS_CLIENT_ID: 57b911ab-1b9d-48e6-b8a0-3ca82a2aeb56
SMS_CLIENT_KEY: aHR0cHM6Ly9wb2tlc3ViLmdvNTJwb2tl
SMS_REQ_TIMEOUT: 30
SMS_VERIFY_EXPIRE: 5  # 分钟

MSF_ODOO_CONFIG:
  # 市本级/东坡区
  "511401":
    enable: true
    name: 市本级/东坡区
    host: *************
    port: 8069
    dbname: msfdp
    user: rhb_api_user
    pwd: ad057f3adac0f8cd38e3c619470643ed7315df8c
  # 天府新区
  "511404":
    enable: true
    name: 天府新区
    host: localhost
    port: 8068
    dbname: msf
    user: admin
    pwd: admin
  # 彭⼭区
  "511403":
    enable: true
    name: 彭⼭区
    host: localhost
    port: 8068
    dbname: msf
    user: admin
    pwd: admin
  # 仁寿县
  "511421":
    enable: true
    name: 仁寿县
    host: *************
    port: 8769
    dbname: msfdp
    user: admin
    pwd: 123abcABC$?
  # 洪雅县
  "511423":
    enable: true
    name: 洪雅县
    host: localhost
    port: 8068
    dbname: msf
    user: admin
    pwd: admin
  # 丹棱县
  "511424":
    enable: true
    name: 丹棱县
    host: localhost
    port: 8068
    dbname: msf
    user: admin
    pwd: admin
  # ⻘神县
  "511425":
    enable: true
    name: ⻘神县
    host: localhost
    port: 8068
    dbname: msf
    user: admin
    pwd: admin