# -*- coding: utf-8 -*-
from enum import Enum


class RedisKeyEnum(Enum):
    @property
    def key(self):
        return self.value.get('key')

    @property
    def remark(self):
        return self.value.get('remark')

    ACCESS_TOKEN = {'key': 'access_token:{key}', 'remark': '登录令牌信息'}
    CAPTCHA = {'key': 'captcha:{key}', 'remark': '验证码信息'}
    ACCOUNT_LOCK = {'key': 'account_lock:{username}', 'remark': '用户锁定'}
    PASSWORD_ERROR_COUNT = {'key': 'password_error_count:{username}', 'remark': '密码错误次数'}
    SMS_PHONE_BZI_NO = {'key': 'sms_bzi:{phone}', 'remark': '短信验证码的业务唯一key'}
    PBA_BASE_CONFIG = {'key': 'pba_base_config:{region_code}', 'remark': '按区县存储一些基础配置信息'}
