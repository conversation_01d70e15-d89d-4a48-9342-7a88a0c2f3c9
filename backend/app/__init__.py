# -*- coding: utf-8 -*-
from contextlib import asynccontextmanager
from pathlib import Path

from fastapi import FastAP<PERSON>, applications
from fastapi.openapi.docs import get_redoc_html, get_swagger_ui_html
from fastapi.staticfiles import StaticFiles
from tortoise import Tortoise

from app.api import <PERSON>f<PERSON>ersonA<PERSON>Router
from app.core.async_queue import start_async_task_queue, stop_async_task_queue
from app.core.config import settings
from app.core.exceptions.handle import TdumpExceptionHandler
from app.core.logger import tdump_log
from app.core.redis import RedisUtil
from app.scripts.initialize.main import InitializeData
from app.utils.common import extract_param_type, get_endwith_method, import_module

import os
import psutil
import time
from fastapi import FastAPI, Request


@asynccontextmanager
async def lifespan(app: FastAPI) -> None:
    """
    生命周期函数
    """

    tdump_log.info(f'{settings.APP_NAME}启动中...')
    # 初始化数据
    await InitializeData().run()

    # for r in app.routes:
    #     print(r)
    # if r.methods:
    #     for method in r.methods:
    #         method_name = get_endwith_method(r.path, method)
    #         method_module = import_module(r.endpoint.__module__)
    #         method_module_name = method_module.__name__
    #         method_module_obj = getattr(method_module, method_module_name)
    #         method_module_obj_method = getattr(method_module_obj, method_name)

    # await start_async_task_queue()

    # redis
    app.state.redis = await RedisUtil.create_redis_poll()
    await RedisUtil.init_sys_dict(app.state.redis)
    await RedisUtil.init_sys_config(app.state.redis)

    tdump_log.info(f'{settings.APP_NAME}启动成功...')

    yield

    # await stop_async_task_queue()
    await RedisUtil.close_redis_pool(app)

    await Tortoise.close_connections()


def register_static_file(app: FastAPI) -> None:
    """
    注册静态文件
    :param app:
    :return:
    """
    if not settings.APP_STATIC_ENABLE:
        return

    p = Path(settings.APP_STATIC_ROOT)
    if not p.exists():
        p.mkdir(settings.APP_STATIC_ROOT)

    app.mount(settings.APP_STATIC_URL, StaticFiles(directory=settings.APP_STATIC_ROOT), name='static')


def register_router(app: FastAPI, prefix: str = '') -> None:
    """
    注册路由
    :param app:
    :param prefix:
    :return:
    """
    app.include_router(MsfPersonApiRouter, prefix='/api')


def register_middlewares(app: FastAPI) -> None:
    """
    自动注册支持中间件
    :param app:
    :return:
    """
    # 请求阶段：后注册的先执行
    # 响应阶段：先注册的后返回
    # 最先注册 → 最后进入请求流程
    # 最后注册 → 最先进入请求流程
    for middleware_item in settings.APP_MIDDLEWARE[::-1]:
        if not middleware_item:
            continue

        middleware, kwargs = middleware_item
        middleware = import_module(middleware)
        app.add_middleware(middleware, **kwargs)


def register_exception(app: FastAPI) -> None:
    """
    添加全局自定义异常捕获
    :param app:
    :return:
    """

    # 自动查找并注册异常处理器
    for _, method in get_endwith_method(TdumpExceptionHandler, endwith='handler'):
        exc_info = extract_param_type(method, param_name='exc')
        app.add_exception_handler(exc_info.get('exc'), method)

    # # 最后手工注册全局异常处理器， 为了保证是最后注册，所有手工注册
    # app.add_exception_handler(Exception, tdump_all_exception)

    # app.add_exception_handler(TdumpException, custom_exception_handler)
    # app.add_exception_handler(TokenAuthException, token_auth_exception_handler)
    # app.add_exception_handler(TokenExpiredException, token_expired_exception_handler)
    # app.add_exception_handler(ParamsValidationException, params_validation_exception_handler)
    # app.add_exception_handler(HTTPException, http_exception_handler)
    # app.add_exception_handler(RequestValidationError, validation_exception_handler)
    # app.add_exception_handler(SQLAlchemyError, sqlalchemy_exception_handler)
    # app.add_exception_handler(Exception, all_exception_handler)


def reset_api_docs() -> None:
    """
    修复Redoc API文档CDN无法访问的问题
    """

    def swagger_monkey_patch(*args, **kwargs):
        """
        修复Swagger API文档CDN无法访问的问题
        """
        return get_swagger_ui_html(
            *args,
            **kwargs,
            swagger_css_url='/static/swagger/swagger-ui/swagger-ui.css',
            swagger_js_url='/static/swagger/swagger-ui/swagger-ui-bundle.js',
            swagger_favicon_url='/static/swagger/favicon.png',
        )

    def redoc_monkey_patch(*args, **kwargs):
        return get_redoc_html(
            *args,
            **kwargs,
            redoc_js_url='/static/swagger/redoc/bundles/redoc.standalone.js',
            redoc_favicon_url='/static/swagger/favicon.png',
        )

    applications.get_swagger_ui_html = swagger_monkey_patch
    applications.get_redoc_html = redoc_monkey_patch


def create_app() -> FastAPI:
    """
    生成FatAPI对象
    :return:
    """
    app = FastAPI(**settings.get_backend_app_attributes, lifespan=lifespan)

    # 跨域设置
    register_middlewares(app)

    # 注册路由
    register_router(app)

    # 注册捕获异常
    register_exception(app)

    # 注册静态文件
    register_static_file(app)
    # 修复Redoc API文档CDN无法访问的问题
    reset_api_docs()

    # @app.middleware("http")
    # async def log_memory(request: Request, call_next):
    #     process = psutil.Process(os.getpid())
    #     mem_before = process.memory_info().rss / 1024 / 1024
    #     start_time = time.time()
    #
    #     print(f"[START] {request.method} {request.url.path} MEM={mem_before:.2f}MB")
    #
    #     response = await call_next(request)
    #
    #     mem_after = process.memory_info().rss / 1024 / 1024
    #     duration = time.time() - start_time
    #     print(f"[END] {request.method} {request.url.path} MEM={mem_after:.2f}MB Δ={mem_after - mem_before:.2f}MB in {duration:.2f}s")
    #
    #     return response

    return app


fast_app = create_app()
