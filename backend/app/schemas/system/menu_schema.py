from typing import Optional

from pydantic import BaseModel, Field

from app.enums.base_enum import MenuType


class BaseMenu(BaseModel):
    id: int
    name: str
    path: str
    remark: Optional[dict]
    menu_type: Optional[MenuType]
    icon: Optional[str]
    order: int
    parent_id: int
    is_hidden: bool
    component: str
    keepalive: bool
    redirect: Optional[str]
    children: Optional[list['BaseMenu']]


class MenuCreate(BaseModel):
    menu_type: MenuType = Field(default=MenuType.CATALOG.value)
    name: str = Field(examples=['用户管理'])
    icon: Optional[str] = 'ph:user-list-bold'
    path: str = Field(examples=['/auth_service/user'])
    order: Optional[int] = Field(examples=[1])
    parent_id: Optional[int] = Field(examples=[0], default=0)
    is_hidden: Optional[bool] = False
    component: str = Field(default='Layout', examples=['/auth_service/user'])
    keepalive: Optional[bool] = True
    redirect: Optional[str] = ''


class MenuUpdate(BaseModel):
    id: int
    menu_type: Optional[MenuType] = Field(examples=[MenuType.CATALOG.value])
    name: Optional[str] = Field(examples=['用户管理'])
    icon: Optional[str] = 'ph:user-list-bold'
    path: Optional[str] = Field(examples=['/auth_service/user'])
    order: Optional[int] = Field(examples=[1])
    parent_id: Optional[int] = Field(examples=[0])
    is_hidden: Optional[bool] = False
    component: str = Field(examples=['/auth_service/user'])
    keepalive: Optional[bool] = False
    redirect: Optional[str] = ''
