# -*- coding: utf-8 -*-
from typing import List, Optional, Union

from pydantic import BaseModel, EmailStr, Field, field_validator, model_validator

from app.utils.common import phone_verifier


class UserCreate(BaseModel):
    username: str = Field(examples=['admin'])
    alias: Optional[str] = Field(None, examples=['别名'])
    email: Optional[EmailStr] = Field(None, examples=['<EMAIL>'])
    phone: str = Field(examples=['13888888888'])
    password: str = Field(examples=['123456'])
    is_active: Optional[bool] = True
    is_superuser: Optional[bool] = False
    role_ids: Optional[List[int]] = []
    dept_id: Optional[int] = Field(0, description='部门ID')

    def create_dict(self):
        return self.model_dump(exclude_unset=True, exclude={'role_ids'})

    @field_validator('phone')
    @classmethod
    def validate_phone(cls, v: Optional[str]) -> Optional[str]:
        return phone_verifier(v)


class UserUpdate(BaseModel):
    id: int
    email: EmailStr
    username: str
    alias: Optional[str] = Field(None, examples=['别名'])
    phone: Optional[str] = Field(None, examples=['13888888888'])
    is_active: Optional[bool] = True
    is_superuser: Optional[bool] = False
    role_ids: Optional[List[int]] = []
    dept_id: Optional[int] = 0

    @field_validator('phone')
    @classmethod
    def validate_phone(cls, v: Optional[str]) -> Optional[str]:
        return phone_verifier(v)


class UpdatePassword(BaseModel):
    old_password: str = Field(description='旧密码')
    new_password: str = Field(description='新密码')


class UserRegister(UserCreate):
    role_ids: List[int] = [2]
    code: str = Field(..., min_length=6, max_length=6, description='手机验证码')

    @model_validator(mode='before')
    @classmethod
    def validate_phone(cls, data: Union[dict, str]) -> Union[dict, str]:
        if isinstance(data, dict):
            if not data.get('phone'):
                data['phone'] = data['username']
        return data


class UserForgetPwd(BaseModel):
    phone: str = Field(examples=['13888888888'])
    code: str = Field(..., min_length=6, max_length=6, description='手机验证码')
    password: str = Field(examples=['123456'])
