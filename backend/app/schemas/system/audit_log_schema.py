# -*- coding: utf-8 -*-
from pydantic import BaseModel, Field


class BaseAuditLog(BaseModel):
    user_id: int = Field(..., description='用户ID', examples=['1'])
    username: str = Field(..., description='用户名称', examples=['1'])
    module: str = Field(..., description='功能模块', examples=['1'])
    summary: str = Field(..., description='请求描述', examples=['1'])
    method: str = Field(..., description='请求方法', examples=['1'])
    path: str = Field(..., description='请求路径', examples=['1'])
    status: str = Field(..., description='状态码', examples=['1'])
    response_time: str = Field(..., description='响应时间(单位ms)', examples=['1'])
    request_args: str = Field(..., description='请求参数', examples=['1'])
    response_body: str = Field(..., description='返回数据', examples=['1'])


class AuditLogCreate(BaseAuditLog): ...


class AuditLogUpdate(BaseAuditLog):
    id: int
