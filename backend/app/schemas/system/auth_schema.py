# -*- coding: utf-8 -*-
from datetime import datetime
from typing import Optional, Union

from pydantic import BaseModel, Field


class CredentialsSchema(BaseModel):
    username: str = Field(..., description='用户名称或手机号', examples=['admin'])
    password: str = Field(..., description='密码', examples=['123456'])

    captcha_key: Optional[str] = Field(default='')
    captcha: Optional[str] = Field(default='')


class JwtPayload(BaseModel):
    sub: str
    user_id: int
    is_refresh: bool
    # last_password_change: Optional[datetime] = None
    is_superuser: (
        bool  # todo diaoyc: 考虑这个是否有问题？？？？ 前端拿到这个生成的Token解密出来有，就知道这个用户是超级用户？
    )
    exp: Union[datetime, int]


class RefreshTokenPayload(BaseModel):
    refresh_token: str


class JwtOut(BaseModel):
    access_token: str
    refresh_token: str
    token_type: str = 'Bearer'
    expires_in: int


class CaptchaOut(BaseModel):
    key: str
    b64_image: str


class SmsPayload(BaseModel):
    phone: str = Field(..., min_length=11, max_length=11, description='手机号')
    captcha_key: Optional[str] = Field(default='')
    captcha: Optional[str] = Field(default='')
