from pydantic import BaseModel, Field

from app.enums.base_enum import MethodType


class BaseApi(BaseModel):
    path: str = Field(..., description='API路径', examples=['/api/v1/user/list'])
    summary: str = Field('', description='API简介', examples=['查看用户列表'])
    method: MethodType = Field(..., description='API方法', examples=['GET'])
    tags: str = Field(..., description='API标签', examples=['User'])


class ApiCreate(BaseApi): ...


class ApiUpdate(BaseApi):
    id: int
