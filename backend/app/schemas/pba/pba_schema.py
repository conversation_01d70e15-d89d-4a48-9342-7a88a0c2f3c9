# -*- coding: utf-8 -*-
from datetime import datetime
from typing import List, Literal, Optional

from pydantic import BaseModel, Field


class VerifyPayload(BaseModel):
    ownership_no: str  # 权属证编号
    ownership_type_code: Literal['1', '2', '3']  # 权属证类型
    owner_name: str  # 权属人名字
    owner_idcard_no: str  # 权属人证件类型
    owner_idcard_type_code: Optional[str] = None


class PersonInfo(BaseModel):
    """
    人员信息基础模型
    """

    name: str  # 姓名
    cert_type_id: int  # 证件类型
    cert_no: str  # 证件号码
    phone: str  # 电话
    address: str = ''  # 联系地址


class HouseInfo(BaseModel):
    """
    房屋信息模型
    """

    # 必填字段
    location: str  # 房屋坐落
    lease_type_id: int  # 租赁类型
    rent_area: float  # 租赁面积
    rent_usage: int  # 租赁用途
    cert_ownerships_type: str  # 权属证类型
    cert_ownerships_no: str  # 权属证号
    house_purpose: str  # 房屋用途
    house_mode: str  # 房屋类型

    # 可选字段
    house_property: str = ''  # 房屋性质
    structure_id: int = ''  # 房屋结构
    hid: str = ''  # 房屋唯一码
    hno: str = ''  # 房屋楼栋
    uno: str = ''  # 单元
    fno: str = ''  # 楼层
    rno: str = ''  # 房号
    inarea: float = 0  # 套内面积
    outarea: float = 0  # 公摊面积
    area: float = 0  # 建筑面积
    rpu_code: str = ''  # 不动产单元号
    mortgaged: str = ''  # 是否抵押
    restricted: str = ''  # 是否查封
    ownership_names: str = ''  # 权利人分号分隔
    house_layout: str = ''  # 房屋户型
    decoration_desc: str = ''  # 房屋装修情况
    elevator_desc: str = ''  # 房屋电梯配置


class ContractCreate(BaseModel):
    """
    PBA基础配置模型
    """

    # 合同基本信息
    payment_type_id: int  # 付款方式
    deposit_type_id: int  # 押金方式
    rent_usage: int  # 租赁用途
    deposit_amt: float  # 押金
    rent_amt: float  # 租金
    start_date: datetime  # 租赁开始日期
    end_date: datetime  # 租赁结束日期
    filing_reason_id: int  # 备案原因
    cert_ownerships_type: str  # 权属证类型
    cert_ownerships_no: str  # 权属证号码
    rhb_residents_no: int = 0  # 居住人数
    rhb_residents_no_max: int = 0  # 居住人数不超过
    allowed_subletting: bool = False  # 是否同意转租
    drafter: Optional[str] = ''  # 合同拟定人
    drafting_time: Optional[datetime] = ''  # 合同拟定时间

    # 出租人信息列表
    lessor_ids: List[PersonInfo] = []

    # 承租人信息列表
    lessee_ids: List[PersonInfo] = []

    # 居住人信息
    resident_ids: Optional[List[PersonInfo]] = []

    # 房屋信息列表
    house_ids: List[HouseInfo] = []


class ContractUpdate(ContractCreate):
    contract_no: str  # 合同编号


class ContractCancel(BaseModel):
    contract_no: str
    reason: str


class FilingDetail(BaseModel):
    filing_no: str
    lessee_name: str
    id_number: str
