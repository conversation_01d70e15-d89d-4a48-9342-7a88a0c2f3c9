# -*- coding: utf-8 -*-
from datetime import datetime
import time
import typing
from typing import Dict, List
from urllib.parse import parse_qs

from fastapi import Request
import shortuuid
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp
# from user_agents import parse

from app.core.context import request, request_var
from app.core.logger import tdump_log
from app.utils import json_helper

# 设置安全限制
MAX_CONTENT_SIZE = 1 * 1024 * 1024  # 1MB限制


async def async_trace_add_log_record(event_type: str = '', msg: Dict = None, remarks: str = ''):
    """

    :param event_type: 日志记录事件描述
    :param msg: 日志记录信息字典
    :param remarks: 日志备注信息
    :return:
    """
    # 如果没有这个标记的属性的，说明这个接口的不需要记录啦！
    if request and hasattr(request.state, 'traceid'):
        # 自增编号索引序
        trace_links_index = request.state.trace_links_index = request.state.trace_links_index + 1
        log = {
            # 自定义一个新的参数复制到我们的请求上下文的对象中
            'traceid': request.state.traceid,
            # 定义链路所以序号
            'trace_index': trace_links_index,
            # 时间类型描述描述
            'event_type': event_type,
            # 日志内容详情
            'msg': msg,
            # 日志备注信息
            'remarks': remarks,
        }
        #  为少少相关记录，删除不必要的为空的日志内容信息，
        if not remarks:
            log.pop('remarks')
        if not msg:
            log.pop('msg')
        try:
            log_msg = json_helper.dict_to_json(log)  # 返回文本
            tdump_log.info(log_msg)
        except Exception:
            tdump_log.error(
                request.state.traceid + '：索引：' + str(request.state.trace_links_index) + ':日志信息写入异常'
            )


class LoggerMiddleware(BaseHTTPMiddleware):
    __slots__ = ('app', 'ignore_url')

    def __init__(
            self,
            app: ASGIApp,
            ignore_url: List = None,
    ) -> None:
        super().__init__(app)
        self.ignore_url = ignore_url or ['/favicon.ico', 'websocket']

    async def dispatch(self, request: Request, call_next):
        path = request.url.path
        # 过滤不需要记录的URL
        if not self._should_log_request(path):
            response = await call_next(request)
            return response

        # 设置追踪ID
        self._make_traceid(request)

        # 设置上下文
        context_token = self.set_context_request(request)

        # 预读取body（支持所有content-type）
        content_type = request.headers.get('content-type', '').lower()
        content_length = int(request.headers.get('content-length', '0'))

        # 只对小内容预读取
        if (0 < content_length <= 1024 * 1024 and  # 1MB限制
                any(ct in content_type for ct in
                    ['application/x-www-form-urlencoded', 'multipart/form-data', 'application/json'])):
            try:
                await request.body()  # 预读取，确保后续调用使用缓存
            except Exception:
                pass

        # 记录请求日志
        log_msg = await self.make_request_log_msg(request)
        await async_trace_add_log_record(event_type='request', msg=log_msg)

        try:
            # 处理请求并捕获响应
            response = await call_next(request)

            # 记录响应日志
            end_time = time.perf_counter()
            process_time = end_time - request.state.start_time

            response_log = {
                'status_code': response.status_code,
                'process_time': f'{process_time:.3f}s',
            }
            await async_trace_add_log_record(
                event_type='response',
                msg=response_log,
            )

            return response

        except Exception as e:
            error_log = {
                'url': request.url.path[:200],
                'method': request.method,
                'ip': request.client.host if request.client else 'unknown',
                'content_type': request.headers.get('content-type', '')[:100],
            }
            # 记录异常日志
            error_log.update({'error': str(e)[:200]})
            await async_trace_add_log_record(event_type='exception', msg=error_log, remarks='请求处理异常')
            raise e
        finally:
            # 重置上下文
            self.reset_context_request(context_token)

    @staticmethod
    def _make_traceid(req) -> None:
        """
        生成追踪链路ID
        :param req:
        :return:
        """
        # 追踪ID
        req.state.traceid = shortuuid.uuid()
        # 追踪索引序号
        req.state.trace_links_index = 0
        # 计算时间
        req.state.start_time = time.perf_counter()

    def _should_log_request(self, path_info: str) -> bool:
        """判断是否需要记录该请求"""
        return not any(ignore_item in path_info for ignore_item in self.ignore_url)

    @staticmethod
    def set_context_request(req):
        """
        生成当前请求上下文对象request
        :param req:
        :return:
        """
        return request_var.set(req)

    @staticmethod
    def reset_context_request(req):
        """
        重置当前请求上下文对象request
        :param req:
        :return:
        """
        request_var.reset(req)

    @staticmethod
    async def _handle_form_urlencoded(result, req):
        # 安全读取URL编码的表单数据
        form_data = await req.form()
        if not form_data:
            return result
        # 只记录非文件字段
        form_dict = {}
        for key, value in form_data.items():
            if not hasattr(value, 'file'):  # 排除文件字段
                # 限制单个字段的大小
                str_value = str(value)
                form_dict[key] = str_value if len(
                    str_value) <= 10000 else f"[TRUNCATED: {len(str_value)} chars]"  # 10KB限制
        result['form_data'] = form_dict
        return result

    @staticmethod
    async def _handle_form_data(result, req):
        # 对于multipart，检查是否包含文件
        form_data = await req.form()
        has_files = any(hasattr(value, 'file') for value in form_data.values())

        if has_files:
            # 如果包含文件，只记录非文件字段的元信息
            result['status'] = 'has_files'
            result['field_count'] = len(form_data)
            result['non_file_fields'] = []
            for key, value in form_data.items():
                if not hasattr(value, 'file'):
                    result['non_file_fields'].append({
                        'name': key,
                        'value_length': len(str(value))
                    })
        else:
            # 没有文件，正常记录
            form_dict = {}
            for key, value in form_data.items():
                str_value = str(value)
                if len(str_value) <= 10000:
                    form_dict[key] = str_value
                else:
                    form_dict[key] = f"[TRUNCATED: {len(str_value)} chars]"
            result['form_data'] = form_dict
        return result

    @staticmethod
    async def _handle_json_data(result, req):
        # JSON数据
        try:
            json_data = await req.json()
            if len(json_data) == 0:
                return result
            # 限制JSON数据的大小
            json_str = str(json_data)
            if len(json_str) <= 50000:  # 50KB限制
                result['json_data'] = json_data
            else:
                result['json_data'] = f"[TRUNCATED: {len(json_str)} chars]"
        except Exception as e:
            result['json_error'] = str(e)
        return result

    @staticmethod
    async def _handle_other_data(result, req):
        # 其他类型，尝试读取为文本
        body_bytes = await req.body()
        if len(body_bytes) == 0:
            return result

        if body_bytes and len(body_bytes) <= 50000:  # 50KB限制
            try:
                result['body_text'] = body_bytes.decode('utf-8')
            except UnicodeDecodeError:
                result['body_text'] = f"[BINARY: {len(body_bytes)} bytes]"
        else:
            result['body_text'] = f"[LARGE_CONTENT: {len(body_bytes)} bytes]"
        return result

    @staticmethod
    async def safe_get_form_and_body(req) -> dict:
        content_type = req.headers.get('content-type', '').lower()
        content_length = int(req.headers.get('content-length', 0))

        result = {
            'content_type': content_type,
            'content_length': content_length,
        }
        # 如果内容过大，不读取
        if content_length > MAX_CONTENT_SIZE:
            result['status'] = 'content_too_large'
            result['message'] = f'Content size {content_length} exceeds limit'
            return result

        try:
            if 'application/x-www-form-urlencoded' in content_type:
                return await LoggerMiddleware._handle_form_urlencoded(result, req)
            if 'multipart/form-data' in content_type:
                return await LoggerMiddleware._handle_form_data(result, req)
            elif 'application/json' in content_type:
                return await LoggerMiddleware._handle_json_data(result, req)
            else:
                return await LoggerMiddleware._handle_other_data(result, req)

        except Exception as e:
            tdump_log.error(str(e))
            result['error'] = str(e)[:200]

        return result

    # noinspection PyBroadException
    async def make_request_log_msg(self, req) -> typing.Dict:
        # 从当前请求中获取到具体的客户端信息
        ip, method, url, headers = req.client.host, req.method, req.url.path, req.headers

        # user_agent = parse(headers['user-agent'])

        log_msg = {
            'url': url[:500],
            # 记录请求方法
            'method': method,
            # 记录请求来源IP
            'ip': ip,
            'accept': headers.get('accept', '')[:200],
            'content-type': headers.get('content-type', '')[:100],
            'tz': headers.get('tz', '')[:50],
            'referer': headers.get('referer', '')[:300],
            'user-agent': headers.get('user-agent', '')[:500],
            # # 记录请求URL信息
            # 'user-agent': {
            #     'ua-string': headers.get('user-agent'),
            #     'os': '{} {}'.format(user_agent.os.family, user_agent.os.version_string),
            #     'browser': '{} {}'.format(user_agent.browser.family, user_agent.browser.version_string),
            #     'device': {
            #         'family': user_agent.device.family,
            #         'brand': user_agent.device.brand,
            #         'model': user_agent.device.model,
            #     },
            # },
            # 记录请求提交的参数信息
            'params': {},
            'ts': f'{datetime.now():%Y-%m-%d %H:%M:%S%z}',
        }

        params = {}
        body = await self.safe_get_form_and_body(req)
        query_params = parse_qs(str(req.query_params))
        if query_params:
            params['query_params'] = query_params
        if body:
            params['body'] = body

        if params:
            log_msg['params'] = params

        return log_msg
