# -*- coding: utf-8 -*-
from datetime import datetime
import time
import typing
from typing import Dict, List
from urllib.parse import parse_qs

from fastapi import Request
import shortuuid
from starlette.types import ASGIApp, Receive, Scope, Send
from user_agents import parse

from app.core.context import request, request_var
from app.core.logger import tdump_log
from app.utils import json_helper


async def async_trace_add_log_record(event_type: str = '', msg: Dict = None, remarks: str = ''):
    """

    :param event_type: 日志记录事件描述
    :param msg: 日志记录信息字典
    :param remarks: 日志备注信息
    :return:
    """
    # 如果没有这个标记的属性的，说明这个接口的不需要记录啦！
    if request and hasattr(request.state, 'traceid'):
        # 自增编号索引序
        trace_links_index = request.state.trace_links_index = request.state.trace_links_index + 1
        log = {
            # 自定义一个新的参数复制到我们的请求上下文的对象中
            'traceid': request.state.traceid,
            # 定义链路所以序号
            'trace_index': trace_links_index,
            # 时间类型描述描述
            'event_type': event_type,
            # 日志内容详情
            'msg': msg,
            # 日志备注信息
            'remarks': remarks,
        }
        #  为少少相关记录，删除不必要的为空的日志内容信息，
        if not remarks:
            log.pop('remarks')
        if not msg:
            log.pop('msg')
        try:
            log_msg = json_helper.dict_to_json(log)  # 返回文本
            tdump_log.info(log_msg)
        except Exception:
            tdump_log.error(
                request.state.traceid + '：索引：' + str(request.state.trace_links_index) + ':日志信息写入异常'
            )


class LoggerMiddleware:
    __slots__ = ('app', 'ignore_url')

    def __init__(
        self,
        app: ASGIApp,
        ignore_url: List = None,
    ) -> None:
        self.app = app
        self.ignore_url = ignore_url or ['/favicon.ico', 'websocket']

    async def __call__(self, scope: Scope, receive: Receive, send: Send) -> None:
        if scope['type'] != 'http':
            await self.app(scope, receive, send)
            return

        # 读取一次
        receive_ = await receive()

        # 并定义一个新协程函数的方式返回一个协程
        async def receive():
            return receive_

        # 创建请求对象用于检查
        request_ = Request(scope, receive)

        path = request_.url.path
        # 过滤不需要记录的URL
        if not self._should_log_request(path):
            await self.app(scope, receive, send)
            return

        # 设置区域代码、请求方向标记和追踪ID
        self._make_traceid(request_)

        # 设置上下文
        context_token = self.set_context_request(request_)

        # # 记录请求开始时间
        # start_time = time.perf_counter()

        # 记录请求日志
        log_msg = await self.make_request_log_msg(request_)
        await async_trace_add_log_record(event_type='request', msg=log_msg)

        try:
            # # 处理请求并捕获响应
            # async def wrapped_send(message):
            #     if message["type"] == "http.response.start":
            #         # 记录响应日志
            #         end_time = time.perf_counter()
            #         process_time = end_time - start_time
            #
            #         response_log = {
            #             "status_code": message.get("status", 0),
            #             "process_time": f"{process_time:.3f}s",
            #         }
            #         await async_trace_add_log_record(
            #             event_type='response',
            #             msg=response_log,
            #         )
            #     await send(message)
            #
            # # 调用下一个中间件
            # response = await self.app(scope, receive, wrapped_send)
            # return response

            response = await self.app(scope, receive, send)
            return response

        except Exception as e:
            # 记录异常日志
            log_msg = await self.make_request_log_msg(request_)
            log_msg.update({'error': str(e)})
            await async_trace_add_log_record(event_type='exception', msg=log_msg, remarks='请求处理异常')
            raise e
        finally:
            # 重置上下文
            self.reset_context_request(context_token)

    @staticmethod
    def _make_traceid(req) -> None:
        """
        生成追踪链路ID
        :param req:
        :return:
        """
        # 追踪ID
        req.state.traceid = shortuuid.uuid()
        # 追踪索引序号
        req.state.trace_links_index = 0
        # 计算时间
        req.state.start_time = time.perf_counter()

    def _should_log_request(self, path_info: str) -> bool:
        """判断是否需要记录该请求"""
        return not any(ignore_item in path_info for ignore_item in self.ignore_url)

    @staticmethod
    def set_context_request(req):
        """
        生成当前请求上下文对象request
        :param req:
        :return:
        """
        return request_var.set(req)

    @staticmethod
    def reset_context_request(req):
        """
        重置当前请求上下文对象request
        :param req:
        :return:
        """
        request_var.reset(req)

    # noinspection PyBroadException
    @staticmethod
    async def get_request_body(req) -> typing.AnyStr:
        body = None
        try:
            body_bytes = await req.body()
            # 添加大小检查 - 减少到 5MB 以节省内存
            max_body_size = 5 * 1024 * 1024  # 5MB
            if len(body_bytes) > max_body_size:
                body = f'Large body omitted ({len(body_bytes)} bytes)'
            else:
                try:
                    body = await req.json()
                except Exception:
                    if body_bytes:
                        try:
                            # 限制字符串长度，防止超大文本占用内存
                            decoded_body = body_bytes.decode('utf-8')
                            if len(decoded_body) > 10000:  # 限制为10K字符
                                body = decoded_body[:10000] + '... (truncated)'
                            else:
                                body = decoded_body
                        except Exception:
                            try:
                                decoded_body = body_bytes.decode('gb2312')
                                if len(decoded_body) > 10000:
                                    body = decoded_body[:10000] + '... (truncated)'
                                else:
                                    body = decoded_body
                            except Exception:
                                body = f'Binary data ({len(body_bytes)} bytes)'
        except Exception:
            pass

        # 不要在请求状态中存储大量数据
        if body and isinstance(body, str) and len(body) > 1000:
            req.state.body = body[:1000] + '... (truncated for state)'
        else:
            req.state.body = body

        return body

    # noinspection PyBroadException
    async def make_request_log_msg(self, req) -> typing.Dict:
        # 从当前请求中获取到具体的客户端信息
        ip, method, url, headers = req.client.host, req.method, req.url.path, req.headers

        user_agent = parse(headers['user-agent'])
        # # 提取UA信息
        # browser = user_agent.browser.version
        # if len(browser) >= 2:
        #     browser_major, browser_minor = browser[0], browser[1]
        # else:
        #     browser_major, browser_minor = 0, 0
        # # 用户当前系统OS信息提取
        # user_os = user_agent.os.version
        # if len(user_os) >= 2:
        #     os_major, os_minor = user_os[0], user_os[1]
        # else:
        #     os_major, os_minor = 0, 0

        log_msg = {
            'url': url,
            # 记录请求方法
            'method': method,
            # 记录请求来源IP
            'ip': ip,
            # 'user-agent': headers.get('user-agent'),
            'accept': headers.get('accept'),
            'content-type': headers.get('content-type'),
            'tz': headers.get('tz'),
            'referer': headers.get('referer'),
            # 记录请求URL信息
            'user-agent': {
                'ua-string': headers.get('user-agent'),
                'os': '{} {}'.format(user_agent.os.family, user_agent.os.version_string),
                'browser': '{} {}'.format(user_agent.browser.family, user_agent.browser.version_string),
                'device': {
                    'family': user_agent.device.family,
                    'brand': user_agent.device.brand,
                    'model': user_agent.device.model,
                },
            },
            # 记录请求提交的参数信息
            'params': {},
            'ts': '',
        }

        # 解析请求提交的表单信息
        try:
            body_form = await req.form()
        except Exception:
            body_form = None
        # 在这里记录下当前提交的body的数据，用于下文的提取
        body = await self.get_request_body(req)

        params = {}
        query_params = parse_qs(str(req.query_params))
        if query_params:
            params['query_params'] = query_params
        if body_form:
            params['from'] = body_form
        if body:
            params['body'] = body

        if params:
            log_msg['params'] = params

        log_msg['ts'] = f'{datetime.now():%Y-%m-%d %H:%M:%S%z}'

        return log_msg
