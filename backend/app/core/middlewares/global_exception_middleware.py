# -*- coding: utf-8 -*-
import traceback

from fastapi import status
from starlette.middleware.base import BaseHTTPMiddleware, RequestResponseEndpoint
from starlette.requests import Request
from starlette.responses import Response

from app.core.logger import tdump_log
from app.core.response import FailResp


class GlobalExceptionMiddleware(BaseHTTPMiddleware):
    """
    全局异常处理中间件
    """

    def __init__(self, app, debug: bool = False):
        super().__init__(app)
        self.debug = debug

    async def dispatch(self, request: Request, call_next: RequestResponseEndpoint) -> Response:
        try:
            responses = await call_next(request)
            return responses
        except Exception as e:
            # 获取traceid（如果存在）
            traceid = getattr(request.state, 'traceid', 'unknown')
            # 记录详细异常日志
            tdump_log.error(
                f'traceid:{traceid},捕获全局异常：{request.method} URL:{request.url}\n'
                f'Headers:{dict(request.headers)}\n'
                f'Exception: {type(e).__name__}: {str(e)}'
            )
            # 打印堆栈跟踪
            traceback.print_exc()
            # 在调试模式下返回更详细的错误信息
            if self.debug:
                message = f'接口异常: {type(e).__name__}: {str(e)}'
            else:
                message = '接口异常'
            # 返回统一的错误响应
            return FailResp(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, code=5000, msg=message)
