from datetime import datetime
import json
import re
from typing import Any, AsyncGenerator

from fastapi import FastAP<PERSON>
from fastapi.responses import Response
from fastapi.routing import APIRoute
from starlette.middleware.base import BaseHTTPMiddleware, RequestResponseEndpoint
from starlette.requests import Request

from app.core.dependency import AuthControl
from app.core.logger import tdump_log
from app.models.system.admin import AuditLog


class HttpAuditLogMiddleware(BaseHTTPMiddleware):
    def __init__(self, app, methods: list[str], exclude_paths: list[str]):
        super().__init__(app)
        self.methods = methods
        self.exclude_paths = exclude_paths
        self.audit_log_paths = ['/api/v1/auditlog/list']
        self.max_body_size = 1024 * 1024  # 1MB 响应体大小限制

    @staticmethod
    async def get_request_args(request: Request) -> dict:
        args = {}
        # 获取查询参数
        for key, value in request.query_params.items():
            args[key] = value

        # 获取请求体
        if request.method in ['POST', 'PUT', 'PATCH']:
            try:
                body = await request.json()
                args.update(body)
            except json.JSONDecodeError:
                try:
                    body = await request.form()
                    args.update(body)
                except Exception:
                    pass

        return args

    async def get_response_body(self, request: Request, response: Response) -> Any:
        # 检查Content-Length
        content_length = response.headers.get('content-length')
        if content_length and int(content_length) > self.max_body_size:
            return {'code': 0, 'msg': 'Response too large to log', 'data': None}

        if hasattr(response, 'body'):
            body = response.body
        else:
            body_chunks = []
            async for chunk in response.body_iterator:
                if not isinstance(chunk, bytes):
                    chunk = chunk.encode(response.charset)
                body_chunks.append(chunk)

            response.body_iterator = self._async_iter(body_chunks)
            body = b''.join(body_chunks)

        if any(request.url.path.startswith(path) for path in self.audit_log_paths):
            try:
                data = self.lenient_json(body)
                # 只保留基本信息，去除详细的响应内容
                if isinstance(data, dict):
                    data.pop('response_body', None)
                    if 'data' in data and isinstance(data['data'], list):
                        for item in data['data']:
                            item.pop('response_body', None)
                return data
            except Exception:
                return None

        return self.lenient_json(body)

    @staticmethod
    def lenient_json(v: Any) -> Any:
        if isinstance(v, (str, bytes)):
            try:
                return json.loads(v)
            except (ValueError, TypeError):
                pass
        return v

    @staticmethod
    async def _async_iter(items: list[bytes]) -> AsyncGenerator[bytes, None]:
        for item in items:
            yield item

    async def get_request_log(self, request: Request, response: Response) -> dict:
        """
        根据request和response对象获取对应的日志记录数据
        """
        data: dict = {'path': request.url.path, 'status': response.status_code, 'method': request.method}
        # 路由信息
        app: FastAPI = request.app
        for route in app.routes:
            if (
                isinstance(route, APIRoute)
                and route.path_regex.match(request.url.path)
                and request.method in route.methods
            ):
                data['module'] = ','.join(route.tags)
                data['summary'] = route.summary
        # 获取用户信息
        try:
            user_id, username = await self.get_user_info(request)
            data['user_id'] = user_id
            data['username'] = username
        except Exception:
            data['user_id'] = 0
            data['username'] = ''
        return data

    @staticmethod
    async def get_user_info(request: Request) -> tuple[int, str]:
        try:
            token = request.headers.get('token')
            if not token:
                return 0, ''

            user_obj = await AuthControl.is_authed(token)
            if not user_obj:
                return 0, ''

            return user_obj.id, user_obj.username
        except Exception as e:
            tdump_log.error(f'Failed to get user info: {str(e)}')
            return 0, ''

    async def before_request(self, request: Request):
        request_args = await self.get_request_args(request)
        request.state.request_args = request_args

    async def after_request(self, request: Request, response: Response, process_time: int):
        if request.method in self.methods:
            for path in self.exclude_paths:
                if re.search(path, request.url.path, re.I) is not None:
                    return
            data: dict = await self.get_request_log(request=request, response=response)
            data['response_time'] = process_time

            data['request_args'] = request.state.request_args

            # 判断是否为二进制数据，如果是则不记录响应结果
            content_type = response.headers.get('content-type', '').lower()
            is_binary = (
                    content_type.startswith('application/pdf') or
                    content_type.startswith('image/') or
                    content_type.startswith('application/octet-stream') or
                    content_type.startswith('application/zip') or
                    'pdf' in content_type
            )
            if not is_binary:
                data['response_body'] = await self.get_response_body(request, response)
            else:
                data['response_body'] = {'message': 'Binary content not logged'}

            try:
                await AuditLog.create(**data)
            except Exception:
                pass

        return response

    async def dispatch(self, request: Request, call_next: RequestResponseEndpoint) -> Response:
        start_time: datetime = datetime.now()
        await self.before_request(request)
        response = await call_next(request)
        end_time: datetime = datetime.now()
        process_time = int((end_time.timestamp() - start_time.timestamp()) * 1000)
        await self.after_request(request, response, process_time)
        return response
