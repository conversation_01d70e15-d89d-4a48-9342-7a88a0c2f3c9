# -*- coding: utf-8 -*-
import datetime
from typing import Dict, Optional

from fastapi import Form, Header
from fastapi.requests import Request
from fastapi.security import OAuth2PasswordBearer, OAuth2PasswordRequestForm
from fastapi.security.utils import get_authorization_scheme_param
from jose import jwt
from passlib.context import Crypt<PERSON>ontext
from passlib.hash import pbkdf2_sha512

from app.core.config import settings
from app.core.exceptions.exception import LoginException, TokenAuthException, TokenExpiredException
from app.schemas.system.auth_schema import JwtPayload


pwd_context = CryptContext(
    schemes=['pbkdf2_sha512'],
    deprecated='auto',
    pbkdf2_sha512__default_rounds=50000,  # 提高安全性
    pbkdf2_sha512__salt_size=32,
)


class CustomOAuth2PasswordBearer(OAuth2PasswordBearer):
    def __init__(
        self,
        token_url=None,
        scheme_name: Optional[str] = None,
        scopes: Optional[Dict[str, str]] = None,
        description: Optional[str] = None,
        auto_error: bool = True,
    ) -> None:
        super().__init__(token_url, scheme_name, scopes, description, auto_error)

    async def __call__(self, request: Request) -> Optional[str]:
        # 从请求头中获取到token
        authorization = request.headers.get('Authorization')
        scheme, token = get_authorization_scheme_param(authorization)
        if not authorization or scheme.lower() != 'bearer':
            if self.auto_error:
                raise TokenAuthException()
                # raise TdumpException(message='请登录后再试', code=stat)
            else:
                return None
        return token


class CustomOAuth2PasswordRequestForm(OAuth2PasswordRequestForm):
    """
    自定义OAuth2PasswordRequestForm类，增加验证码类
    """

    def __init__(
        self,
        username: str = Form(),
        password: str = Form(),
        captcha_key: Optional[str] = Form(default=''),
        captcha: Optional[str] = Form(default=''),
    ):
        super().__init__(username=username, password=password)
        self.captcha_key = captcha_key
        self.captcha = captcha


OAuth2Schema = CustomOAuth2PasswordBearer(token_url='/api/v1/base/access_token')


def generate_access_token(payload: JwtPayload, **kwargs):
    """
    生产token
    :param payload:
    :param kwargs:
    :return:
    """
    payload_dict = payload.model_dump()
    payload_dict['iat'] = datetime.datetime.now(datetime.UTC)
    payload_dict['username'] = payload.sub
    # to_encode = {  # 标准声明
    #     "sub": str(subject),
    #     "exp": expire,
    #     "iat": datetime.datetime.now(datetime.UTC),
    #     # # 公共声明
    #     # "name": "李四",
    #     # "email": "<EMAIL>",
    #     # "roles": ["finance", "approver"],
    #     #
    #     # # 私有声明
    #     # "internal_code": "A-1001",
    #     # "custom_settings": {"dark_mode": True}
    # }
    if kwargs:
        payload_dict.update(kwargs)
    token = jwt.encode(payload_dict, key=settings.JWT_SECRET_KEY, algorithm=settings.JWT_ALGORITHM)

    return token


def decode_access_token_from_header(token: Optional[str] = Header(None, alias='Authorization')) -> JwtPayload:
    """
    解析验证token  默认验证headers里面为token字段的数据
    可以给 headers 里面token替换别名, 以下示例为 X-Token
    token: Optional[str] = Header(None, alias="X-Token")
    :param token:
    :return:
    """
    scheme, _, token = token.partition(' ')
    return decode_access_token(token)


def decode_access_token(token: Optional[str]) -> JwtPayload:
    if not token:
        raise LoginException(message='请登录后再试')

    try:
        payload = jwt.decode(token, key=settings.JWT_SECRET_KEY, algorithms=settings.JWT_ALGORITHM)
        username = payload.get('sub')
        if not username:
            raise LoginException(message='请登录后再试')
        return JwtPayload(**payload)
    except jwt.ExpiredSignatureError as err:
        raise TokenExpiredException() from err
    except (jwt.JWTError, AttributeError) as err:
        raise TokenAuthException() from err


def generate_hash_password(password: str) -> str:
    """
    生成hash密码
    :param password:
    :return:
    """
    return pwd_context.hash(password)


def verify_hashed_password(hashed_password: str) -> bool:
    try:
        identified = pbkdf2_sha512.identify(hashed_password)
        return identified
    except Exception:
        return False


def verify_plain_password(plain_pwd: str, hashed_pwd: str) -> bool:
    """
    验证密码
    :param plain_pwd:
    :param hashed_pwd:
    :return:
    """
    return pwd_context.verify(plain_pwd, hashed_pwd)
