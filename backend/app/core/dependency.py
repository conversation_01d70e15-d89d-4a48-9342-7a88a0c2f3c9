from typing import Optional

from fastapi import Depends, HTTPException, Request
from jose import jwt

from app.core.context import CTX_USER_ID
from app.core.exceptions.exception import AccessException, TdumpException, TokenAuthException, TokenExpiredException
from app.core.logger import tdump_log
from app.core.security import OAuth2Schema, decode_access_token
from app.models import Role, User


class AuthControl:
    @classmethod
    # async def is_authed(cls, token: str = Header(..., description='token验证')) -> Optional['User']:
    async def is_authed(cls, token: str = Depends(OAuth2Schema)) -> Optional['User']:
        try:
            if token == 'dev':
                user = await User.filter().first()
                user_id = user.id
            else:
                decode_data = decode_access_token(token)
                user_id = decode_data.user_id
            user = await User.filter(id=user_id).first()
            if not user:
                raise AccessException(message='Authentication failed')
            CTX_USER_ID.set(int(user_id))
            return user
        except jwt.ExpiredSignatureError as e:
            raise TokenExpiredException() from e
        except jwt.JWTError as e:
            raise TokenAuthException() from e
        except Exception as e:
            tdump_log.error(repr(e))
            raise TdumpException(message='解析token失败') from e


class PermissionControl:
    @classmethod
    async def has_permission(cls, request: Request, current_user: User = Depends(AuthControl.is_authed)) -> None:
        if current_user.is_superuser:
            return
        method = request.method
        # path = request.url.path
        path = request.scope['route'].path
        roles: list[Role] = await current_user.roles
        if not roles:
            raise HTTPException(status_code=403, detail='The user is not bound to a role')
        apis = []
        for role in roles:
            apis.extend(await role.apis)
        permission_apis = {(api.method, api.path) for api in apis}
        # path = "/api/v1/auth/userinfo"
        # method = "GET"
        if (method, path) not in list(permission_apis):
            raise HTTPException(status_code=403, detail=f'Permission denied method:{method} path:{path}')


DependAuth = Depends(AuthControl.is_authed)
DependPermission = Depends(PermissionControl.has_permission)
