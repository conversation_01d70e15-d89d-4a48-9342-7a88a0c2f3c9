# -*- coding: utf-8 -*-
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Union

from pydantic_settings import BaseSettings
import yaml


class AppSettings(BaseSettings):
    """
    系统配置
    """

    """
    # ================================================= #
    # ******************* APP基础配置 ****************** #
    # ================================================= #
    """
    # 开发环境模式配置
    APP_ENV: str = 'dev'

    APP_RELOAD: bool = True

    # 项目根路径
    APP_BASE_DIR: Path = Path(__file__).parent.parent

    # 主机IP
    APP_SERVER_HOST: str = '0.0.0.0'
    # 主机端口
    APP_SERVER_PORT: int = 8000
    # API前缀
    APP_API_PREFIX: str = ''
    # 是否允许一个账号同时登录
    APP_SAME_TIME_LOGIN: bool = True

    """
    # ================================================= #
    # ******************* API文档配置 ****************** #
    # ================================================= #
    """
    # 项目文档
    APP_NAME: str = 'MsfPersonAdmin'
    APP_DESCRIPTION: Optional[str] = 'MsfPersonAdminAPI'
    APP_VERSION: str = '1.0'
    # 文档地址 默认为docs
    APP_DOCS_URL: str = f'{APP_API_PREFIX}/docs'
    # 文档关联请求数据接口
    APP_OPENAPI_URL: str = f'{APP_API_PREFIX}/openapi.json'
    # redoc 文档
    APP_REDOC_URL: Optional[str] = f'{APP_API_PREFIX}/redoc'

    @property
    def get_backend_app_attributes(self) -> Dict[str, Union[str, bool, None]]:
        """
        设置 `FastAPI` 自定义属性
        """
        return {
            'debug': True if self.APP_ENV == 'dev' else False,
            'title': self.APP_NAME,
            'version': self.APP_VERSION,
            'description': self.APP_DESCRIPTION,
            'docs_url': self.APP_DOCS_URL,
            'openapi_url': self.APP_OPENAPI_URL,
            'redoc_url': self.APP_REDOC_URL,
            'root_path': self.APP_API_PREFIX,
        }

    """
    # ================================================= #
    # ***************** 静态文件目录配置 ***************** #
    # ================================================= #
    """
    # 是否启用静态文件目录访问
    APP_STATIC_ENABLE: bool = True
    # 路由访问
    APP_STATIC_URL: str = '/static'
    # 静态文件目录名
    APP_STATIC_DIR: str = 'static'
    # 静态文件目录绝对路径
    APP_STATIC_ROOT: Path = APP_BASE_DIR.joinpath(APP_STATIC_DIR)

    """
    # ================================================= #
    # ********************* 日志配置 ******************* #
    # ================================================= #
    """
    # 是否开启每次操作日志记录到数据库
    APP_AUDITLOG_RECORD: bool = True
    # 只记录包括的请求方式记录到数据库
    APP_AUDITLOG_RECORD_METHOD: List[str] = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH']
    # 需要忽略记录审计的接口地址
    APP_AUDITLOG_RECORD_EXCLUDE: List[str] = ['/api/v1/base/access_token', '/docs', '/openapi.json', '/static']
    """
    # ================================================= #
    # ******************** 跨域配置 ******************** #
    # ================================================= #
    """
    # 是否启用跨域
    APP_CORS_ORIGIN_ENABLE: bool = True
    # 只允许访问的域名列表, * 代表所有
    APP_ALLOW_ORIGINS: List[str] = ['*']
    # 允许跨域的http方法, 例如 get、post、put 等
    APP_ALLOW_METHODS: List[str] = ['*']
    # 允许携带的headers, 可以用来鉴别来源等
    APP_ALLOW_HEADERS: List[str] = ['*']
    # 是否支持携带 cookie
    APP_ALLOW_CREDENTIALS: bool = True

    @property
    def get_cors_middleware_attributes(self) -> Dict[str, Union[List[str], bool]]:
        """
        设置 `CORSMiddleware` 自定义属性
        """
        return {
            'allow_origins': self.APP_ALLOW_ORIGINS,
            'allow_methods': self.APP_ALLOW_METHODS,
            'allow_headers': self.APP_ALLOW_HEADERS,
            'allow_credentials': self.APP_ALLOW_CREDENTIALS,
        }

    """
    # ================================================= #
    # ******************* 登录认证配置 ****************** #
    # ================================================= #
    """
    # 生产环境保管好 token的SECRET_KEY
    JWT_SECRET_KEY: str = 'aeq)s(*&(&)()WEQasd8**&^9asda_asdasd*&*&^+_sda'
    # 生成token的加密算法
    JWT_ALGORITHM: str = 'HS256'
    # access_token 过期时间
    JWT_ACCESS_TOKEN_EXPIRE_MINUTES: int = 60 * 24
    # refresh_token 过期时间
    JWT_REFRESH_TOKEN_EXPIRE_MINUTES: int = 60 * 24 * 7
    # # jwt token 过期时间
    # JWT_REDIS_EXPIRE_MINUTES: int = 30
    DEFAULT_PASSWORD: Union[str, int] = '123456'
    """
    # ================================================= #
    # ******************** 验证码配置 ******************* #
    # ================================================= #
    """
    # 是否开启登录验证码功能
    CAPTCHA_ENABLE: bool = True
    # 是否启用验证码小写验证
    CAPTCHA_LOWER_ENABLE: bool = False
    # 验证码过期时间
    CAPTCHA_EXPIRE_SECONDS: int = 60

    """
    # ================================================= #
    # ******************** 数据库配置 ******************* #
    # ================================================= #
    """
    TORTOISE_ORM: dict = {
        'connections': {
            # SQLite configuration
            # 'sqlite': {
            #     'engine': 'tortoise.backends.sqlite',
            #     'credentials': {'file_path': f'{APP_BASE_DIR}/db.sqlite3'},  # Path to SQLite database file
            # },
            # MySQL/MariaDB configuration
            # Install with: tortoise-orm[asyncmy]
            # "mysql": {
            #     "engine": "tortoise.backends.mysql",
            #     "credentials": {
            #         "host": "localhost",  # Database host address
            #         "port": 3306,  # Database port
            #         "user": "yourusername",  # Database username
            #         "password": "yourpassword",  # Database password
            #         "database": "yourdatabase",  # Database name
            #     },
            # },
            # PostgreSQL configuration
            # Install with: tortoise-orm[asyncpg]
            'postgres': {
                'engine': 'tortoise.backends.asyncpg',
                'credentials': {
                    'host': 'localhost',  # Database host address
                    'port': 5432,  # Database port
                    'user': 'yourusername',  # Database username
                    'password': 'yourpassword',  # Database password
                    'database': 'yourdatabase',  # Database name
                },
            },
            # MSSQL/Oracle configuration
            # Install with: tortoise-orm[asyncodbc]
            # "oracle": {
            #     "engine": "tortoise.backends.asyncodbc",
            #     "credentials": {
            #         "host": "localhost",  # Database host address
            #         "port": 1433,  # Database port
            #         "user": "yourusername",  # Database username
            #         "password": "yourpassword",  # Database password
            #         "database": "yourdatabase",  # Database name
            #     },
            # },
            # SQLServer configuration
            # Install with: tortoise-orm[asyncodbc]
            # "sqlserver": {
            #     "engine": "tortoise.backends.asyncodbc",
            #     "credentials": {
            #         "host": "localhost",  # Database host address
            #         "port": 1433,  # Database port
            #         "user": "yourusername",  # Database username
            #         "password": "yourpassword",  # Database password
            #         "database": "yourdatabase",  # Database name
            #     },
            # },
        },
        'apps': {
            'models': {
                'models': ['app.models', 'aerich.models'],
                'default_connection': 'postgres',
            },
        },
        'use_tz': False,  # Whether to use timezone-aware datetimes
        'timezone': 'Asia/Shanghai',  # Timezone setting
    }
    DATETIME_FORMAT: str = '%Y-%m-%d %H:%M:%S'

    """
    # ================================================= #
    # ******************** Redis配置 ******************* #
    # ================================================= #
    """
    REDIS_ENABLE: bool = True
    # REDIS_URL: RedisDsn = "redis://127.0.0.1:6379/0"
    REDIS_HOST: str = '127.0.0.1'
    REDIS_PORT: int = 6379
    REDIS_USERNAME: str = ''
    REDIS_PASSWORD: str = ''
    REDIS_DATABASE: int = 2

    """
    # ================================================= #
    # ***************** 文件上传配置 ******************** #
    # ================================================= #
    """
    # 文件上传路径
    UPLOAD_PATH: str = '/static/upload'
    # 文件上传大小限制
    UPLOAD_SIZE: float = 5 * 1024 * 1024
    DEFAULT_ALLOWED_EXTENSION: List[str] = [
        'bmp',
        'gif',
        'jpg',
        'jpeg',
        'png',
        'doc',
        'docx',
        'xls',
        'xlsx',
        'ppt',
        'pptx',
        'html',
        'htm',
        'txt',
        'rar',
        'zip',
        'gz',
        'bz2',
        'mp4',
        'avi',
        'rmvb',
        'pdf',
    ]

    """
    # ================================================= #
    # ******************** 中间件配置 ******************* #
    # ================================================= #
    """
    APP_MIDDLEWARE: List[Optional[Tuple[str, Dict]]] = [
        # 1. CORS中间件（最外层）
        ('app.core.middlewares.CustomCORSMiddleware', {}) if APP_CORS_ORIGIN_ENABLE else None,
        # 2. 全局异常处理中间件（第二层）
        ('app.core.middlewares.GlobalExceptionMiddleware', {'debug': APP_ENV == 'dev'}),
        # 3. 日志中间件（第三层）
        ('app.core.middlewares.LoggerMiddleware', {'ignore_url': ['/favicon.ico', 'websocket']}),
        # 4. AuditLog
        (
            'app.core.middlewares.HttpAuditLogMiddleware',
            {'methods': APP_AUDITLOG_RECORD_METHOD, 'exclude_paths': APP_AUDITLOG_RECORD_EXCLUDE},
        )
        if APP_AUDITLOG_RECORD
        else None,
    ]

    """
    # ================================================= #
    # ******************** odoo配置 ******************* #
    # ================================================= #
    """

    MSF_ODOO_CONFIG: Dict = {}

    MSF_REGION_CODE: List[str] = [k for k, v in MSF_ODOO_CONFIG.items()]

    @classmethod
    def from_yaml(cls):
        yaml_file = Path(__file__).parent.parent.parent / 'config.yml'

        yaml_config = {}
        if yaml_file.exists():
            with yaml_file.open('r', encoding='utf-8') as f:
                yaml_config = yaml.safe_load(f)
                yaml_config['MSF_REGION_CODE'] = [k for k, v in yaml_config.get('MSF_ODOO_CONFIG', {}).items()]

        return cls(**yaml_config)

    """
    # ================================================= #
    # ******************** 验证码配置 ******************* #
    # ================================================= #
    """

    SMS_HOST: str = '*************:8169'
    SMS_URL: str = f'http://{SMS_HOST}'
    SMS_SEND_URL: str = f'http://{SMS_HOST}/msf_sms/send_verification_code'
    SMS_VERIFY_URL: str = f'http://{SMS_HOST}/msf_sms/sms_code_verify'
    SMS_CLIENT_ID: str = '57b911ab-1b9d-48e6-b8a0-3ca82a2aeb56'
    SMS_CLIENT_KEY: str = 'aHR0cHM6Ly9wb2tlc3ViLmdvNTJwb2tl'
    SMS_REQ_TIMEOUT: int = 30
    SMS_VERIFY_EXPIRE: int = 5  # 分钟


settings = AppSettings.from_yaml()

__all__ = ['settings']
