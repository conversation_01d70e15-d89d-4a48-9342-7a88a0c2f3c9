# -*- coding: utf-8 -*-

from contextvars import ContextVar

from fastapi import Request


def bind_context_var(context_var):
    class ContextVarBind:
        __slots__ = ()

        def __getattr__(self, name):
            return getattr(context_var.get(), name)

        def __setattr__(self, name, value):
            setattr(context_var.get(), name, value)

        def __delattr__(self, name):
            delattr(context_var.get(), name)

        def __getitem__(self, index):
            return context_var.get()[index]

        def __setitem__(self, index, value):
            context_var.get()[index] = value

        def __delitem__(self, index):
            del context_var.get()[index]

    return ContextVarBind()


CTX_USER_ID: ContextVar[int] = ContextVar('user_id', default=0)

request_var: ContextVar[Request] = ContextVar('request')
request: ContextVar[Request] = bind_context_var(request_var)
