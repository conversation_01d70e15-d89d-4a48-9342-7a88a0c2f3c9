# -*- coding: utf-8 -*-
import traceback

from fastapi import Request, status
from fastapi.exceptions import HTTPException, RequestValidationError, ResponseValidationError
from fastapi.responses import JSONResponse
from pydantic import ValidationError as PydanticValidationError
from tortoise.exceptions import DoesNotExist, IntegrityError

from app.core.exceptions.exception import (
    AccessException,
    ExceptionEnum,
    LoginException,
    NotFoundException,
    OdooRespException,
    ParamsValidationException,
    SmsException,
    TdumpException,
    TokenAuthException,
    TokenExpiredException,
)
from app.core.logger import tdump_log
from app.core.response import FailResp


class TdumpExceptionHandler:
    """
    将异常处理器放入一个类中，方便自动注入到FastAPI对象中， 也方便继承
    异常处理器也可以不放入到类中，不过得手工注册异常捕获器
    """

    @staticmethod
    async def custom_exception_handler(request: Request, exc: TdumpException) -> JSONResponse:
        """
        自定义异常处理器
        """
        tdump_log.error(f'请求地址：{request.url.__str__()}: {exc.message}: {exc.desc}')
        return FailResp(status_code=exc.status_code, code=exc.code, msg=exc.message)

    @staticmethod
    async def login_exception_handler(request: Request, exc: LoginException) -> JSONResponse:
        """
        登录异常
        :param request:
        :param exc:
        :return:
        """
        tdump_log.error(f'请求地址：{request.url.__str__()}: {exc.message}: {exc.desc}')
        return FailResp(status_code=exc.status_code, code=exc.code, msg=exc.message)

    @staticmethod
    async def access_exception_handler(request: Request, exc: AccessException) -> JSONResponse:
        """

        :param request:
        :param exc:
        :return:
        """
        tdump_log.error(f'请求地址：{request.url.__str__()}: {exc.message}: {exc.desc}')
        return FailResp(status_code=exc.status_code, code=exc.code, msg=exc.message)

    @staticmethod
    async def token_auth_exception_handler(request: Request, exc: TokenAuthException) -> JSONResponse:
        """
        token 验证异常
        :param request:
        :param exc:
        :return:
        """
        tdump_log.error(f'请求地址：{request.url.__str__()}: {exc.message}: {exc.desc}')
        return FailResp(status_code=exc.status_code, code=exc.code, msg=exc.message)

    @staticmethod
    async def token_expired_exception_handler(request: Request, exc: TokenExpiredException) -> JSONResponse:
        """
        token 验证异常
        :param request:
        :param exc:
        :return:
        """
        tdump_log.error(f'请求地址：{request.url.__str__()}: {exc.message}: {exc.desc}')
        return FailResp(status_code=exc.status_code, code=exc.code, msg=exc.message)

    @staticmethod
    async def params_validation_exception_handler(request: Request, exc: ParamsValidationException) -> JSONResponse:
        """
        参数验证异常
        :param request:
        :param exc:
        :return:
        """
        tdump_log.error(f'请求地址：{request.url.__str__()}: {exc.message}: {exc.desc}')
        return FailResp(status_code=exc.status_code, code=exc.code, msg=exc.message)

    @staticmethod
    async def pydantic_validation_exception_handler(request: Request, exc: PydanticValidationError) -> JSONResponse:
        """
        Pydantic 验证异常
        :param request:
        :param exc:
        :return:
        """
        tdump_log.error(f'请求地址：{request.url.__str__()}: {exc.args}')
        traceback.print_exc()
        return FailResp(
            status_code=status.HTTP_400_BAD_REQUEST,
            code=ExceptionEnum.PARAMETER.value[0],
            msg=ExceptionEnum.PARAMETER.value[1],
        )

    @staticmethod
    async def http_exception_handler(request: Request, exc: HTTPException) -> JSONResponse:
        """
        重写HTTPException异常处理器
        """
        tdump_log.error(f'请求地址：{request.url.__str__()}: {exc.status_code}: {exc.detail}')

        if exc.status_code == status.HTTP_405_METHOD_NOT_ALLOWED:
            return FailResp(
                status_code=status.HTTP_405_METHOD_NOT_ALLOWED,
                code=ExceptionEnum.METHOD_NOT_ALLOWED.value[0],
                msg=ExceptionEnum.METHOD_NOT_ALLOWED.value[1],
            )

        if exc.status_code == status.HTTP_404_NOT_FOUND:
            return FailResp(
                status_code=status.HTTP_404_NOT_FOUND,
                code=ExceptionEnum.NOT_FOUND.value[0],
                msg=ExceptionEnum.NOT_FOUND.value[1],
            )

        elif exc.status_code == status.HTTP_429_TOO_MANY_REQUESTS:
            return FailResp(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                code=ExceptionEnum.LIMITER.value[0],
                msg=ExceptionEnum.LIMITER.value[1],
            )
        elif exc.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR:
            return FailResp(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                code=ExceptionEnum.INTERNAL_ERROR.value[0],
                msg=ExceptionEnum.INTERNAL_ERROR.value[1],
            )
        elif exc.status_code == status.HTTP_400_BAD_REQUEST:
            return FailResp(
                status_code=status.HTTP_400_BAD_REQUEST, code=ExceptionEnum.BAD_REQUEST.value[0], msg=exc.detail
            )

        return FailResp(status_code=exc.status_code, code=ExceptionEnum.INTERNAL_ERROR.value[0], msg=exc.detail)

    @staticmethod
    async def req_validation_exception_handler(request: Request, exc: RequestValidationError) -> JSONResponse:
        """
        重写请求验证异常处理器
        """
        tdump_log.error(f'请求地址：{request.url.__str__()}: {exc.errors()}')
        msg = exc.errors()[0].get('message') or exc.errors()[0].get('msg')
        return FailResp(status_code=status.HTTP_400_BAD_REQUEST, code=ExceptionEnum.PARAMETER.value[0], msg=msg)

    @staticmethod
    async def resp_validation_exception_handler(request: Request, exc: ResponseValidationError) -> JSONResponse:
        """
        重写响应验证异常处理器
        """
        tdump_log.error(f'请求地址：{request.url.__str__()}: {exc.errors()}')
        msg = exc.errors()[0].get('message')
        return FailResp(status_code=status.HTTP_400_BAD_REQUEST, code=ExceptionEnum.PARAMETER.value[0], msg=msg)

    @staticmethod
    async def not_fund_exception_handler(request: Request, exc: NotFoundException) -> JSONResponse:
        """

        :param request:
        :param exc:
        :return:
        """
        tdump_log.error(f'请求地址：{request.url.__str__()}: {exc.__str__()}')
        return FailResp(status_code=exc.status_code, code=exc.code, msg=exc.message)

    @staticmethod
    async def orm_does_not_exist_handler(request: Request, exc: DoesNotExist) -> JSONResponse:
        tdump_log.error(f'请求地址：{request.url.__str__()}: {exc.__str__()}')
        return FailResp(status_code=404, code=404, msg='未找到记录')

    @staticmethod
    async def orm_integrity_error_handler(request: Request, exc: IntegrityError) -> JSONResponse:
        tdump_log.error(f'请求地址：{request.url.__str__()}: {exc.__str__()}')
        return FailResp(
            status_code=status.HTTP_400_BAD_REQUEST, code=ExceptionEnum.PARAMETER.value[0], msg=str(exc.args)
        )

    @staticmethod
    async def sms_miss_exception_handler(request: Request, exc: SmsException) -> JSONResponse:
        tdump_log.error(f'请求地址：{request.url.__str__()}: {exc.message}')
        return FailResp(status_code=exc.status_code, code=exc.code, msg=exc.message)

    @staticmethod
    async def odoo_resp_exception_handler(request: Request, exc: OdooRespException) -> JSONResponse:
        tdump_log.error(f'请求地址：{request.url.__str__()}: {exc.message}')
        return FailResp(status_code=exc.status_code, code=exc.code, msg=exc.message)
