# -*- coding: utf-8 -*-
from enum import Enum

from fastapi import status


class ExceptionEnum(Enum):
    SUCCESS = ('200', '操作成功')
    FAILED = ('-1', '操作失败')
    BAD_REQUEST = ('400', '错误的请求')
    UNAUTHORIZED = ('401', '未经许可授权')
    FORBIDDEN = ('403', '失败！当前访问没有权限，或操作的数据没权限!')
    NOT_FOUND = ('404', '访问对象不存在')
    METHOD_NOT_ALLOWED = ('405', '不允许使用此方法提交访问')
    PARAMETER = ('422', '参数校验错误,请检查提交的参数信息')
    LIMITER = ('429', '访问的速度过快')
    OTHER = ('500', '未知的其他异常')
    INTERNAL_ERROR = ('500', '程序员哥哥睡眠不足，系统崩溃了！')
    INVALID_TOKEN = ('403', 'token无效')
    EXPIRED_TOKEN = ('403', 'token过期')
    FILE_TOO_LARGE = ('413', '文件体积过大')
    FILE_TOO_MANY = ('4133', '文件数量过多')
    FILE_EXTENSION = ('10041', '文件扩展名不符合规范')
    BUSINESS_ERROR = ('0000', '业务错误逻辑处理')
    LOGIN_ERROR = ('401', '登录失败')
    SMS_ERROR = ('405', '短信验证码验证失败')


class TdumpException(Exception):
    __slots__ = ['message', 'code', 'status_code', 'desc']

    def __init__(
        self,
        code: str = status.HTTP_500_INTERNAL_SERVER_ERROR,
        message: str = None,
        status_code: int = status.HTTP_200_OK,
        desc: str = None,
    ) -> None:
        self.message = message
        self.code = code
        self.status_code = status_code
        self.desc = desc


class NotFoundException(TdumpException):
    def __init__(
        self, code: str = None, message: str = None, status_code: int = status.HTTP_404_NOT_FOUND, desc: str = None
    ):
        self.code = code or ExceptionEnum.NOT_FOUND.value[0]
        self.message = message or ExceptionEnum.NOT_FOUND.value[1]
        super(NotFoundException, self).__init__(self.code, self.message, status_code, desc)


class LoginException(TdumpException):
    def __init__(
        self, code: str = None, message: str = None, status_code: int = status.HTTP_401_UNAUTHORIZED, desc: str = None
    ):
        self.code = code or ExceptionEnum.LOGIN_ERROR.value[0]
        self.message = message or ExceptionEnum.LOGIN_ERROR.value[1]
        super(LoginException, self).__init__(self.code, self.message, status_code, desc)


class AccessException(TdumpException):
    def __init__(
        self, code: str = None, message: str = None, status_code: int = status.HTTP_401_UNAUTHORIZED, desc: str = None
    ):
        self.code = code or ExceptionEnum.UNAUTHORIZED.value[0]
        self.message = message or ExceptionEnum.UNAUTHORIZED.value[1]
        super(AccessException, self).__init__(self.code, self.message, status_code, desc)


class TokenAuthException(TdumpException):
    def __init__(
        self, code: str = None, message: str = None, status_code: int = status.HTTP_401_UNAUTHORIZED, desc: str = None
    ):
        self.code = code or ExceptionEnum.INVALID_TOKEN.value[0]
        self.message = message or ExceptionEnum.INVALID_TOKEN.value[1]
        super(TokenAuthException, self).__init__(self.code, self.message, status_code, desc)


class TokenExpiredException(TdumpException):
    def __init__(
        self, code: str = None, message: str = None, status_code: int = status.HTTP_401_UNAUTHORIZED, desc: str = None
    ):
        self.code = code or ExceptionEnum.EXPIRED_TOKEN.value[0]
        self.message = message or ExceptionEnum.EXPIRED_TOKEN.value[1]
        super(TokenExpiredException, self).__init__(self.code, self.message, status_code, desc)


class ParamsValidationException(TdumpException):
    def __init__(
        self, code: str = None, message: str = None, status_code: int = status.HTTP_400_BAD_REQUEST, desc: str = None
    ):
        self.code = code or ExceptionEnum.PARAMETER.value[0]
        self.message = message or ExceptionEnum.PARAMETER.value[1]
        super(ParamsValidationException, self).__init__(self.code, self.message, status_code, desc)


class SmsException(TdumpException):
    def __init__(
        self,
        code: str = None,
        message: str = None,
        status_code: int = status.HTTP_401_UNAUTHORIZED,
        desc: str = None,
    ):
        self.code = code or ExceptionEnum.SMS_ERROR.value[0]
        self.message = message or ExceptionEnum.SMS_ERROR.value[1]
        super(SmsException, self).__init__(self.code, self.message, status_code, desc)


class OdooRespException(TdumpException):
    def __init__(
        self, code: str = None, message: str = None, status_code: int = status.HTTP_400_BAD_REQUEST, desc: str = None
    ):
        self.code = code
        self.message = message
        super(OdooRespException, self).__init__(self.code, self.message, status_code, desc)
