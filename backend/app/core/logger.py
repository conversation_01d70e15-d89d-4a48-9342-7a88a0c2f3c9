# -*- coding: utf-8 -*-
from pathlib import Path
import sys
import time
from typing import Dict

from loguru import logger


class LoggerInitializer:
    def __init__(self):
        self.log_path: Path = Path(__file__).parent.parent
        self.__ensure_log_dir_exists()
        self.log_path_info = self.log_path.joinpath(f'log/info_{time.strftime("%Y-%m-%d")}.log')
        self.log_path_error = self.log_path.joinpath(f'log/error_{time.strftime("%Y-%m-%d")}.log')

    def __ensure_log_dir_exists(self):
        if not self.log_path.exists():
            self.log_path.mkdir(parents=True, exist_ok=True)

    @staticmethod
    def __filter(log: Dict):
        # log['trace_id'] = TraceCtx.get_id()
        return log

    def init_log(self):
        # 自定义日志格式
        log_format_str = (
            '<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | '
            '<cyan>thread_id:{thread.id}</cyan> | '
            '<cyan>thread_name:{thread.name}</cyan> | '
            '<level>{level: <8}</level> | '
            '<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - '
            '<level>{message}</level>'
        )

        # 这句话很关键避免多次的写入我们的日志
        # logger.configure(handlers=[{'sink': sys.stdout, 'format': log_format_str}])
        # 这个也可以启动避免多次的写入的作用，但是我们的 app:register_logger:40 -无法输出

        logger.remove()
        # 移除后重新添加sys.stderr, 目的: 控制台输出与文件日志内容和结构一致
        logger.add(sys.stdout, filter=self.__filter, format=log_format_str, enqueue=True)
        # todo diaoyc: 暂时先不写入日志文件
        # logger.add(self.log_path_info, filter=self.__filter, format=log_format_str, rotation='00:00',
        #            retention='60 days', enqueue=True, encoding='UTF-8', level='INFO', compression='zip')
        # logger.add(self.log_path_error, filter=self.__filter, format=log_format_str, rotation='00:00',
        #            retention='60 days', enqueue=True, encoding='UTF-8', level='ERROR', compression='zip')

        return logger


tdump_log = LoggerInitializer().init_log()
