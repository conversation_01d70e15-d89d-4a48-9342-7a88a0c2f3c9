# -*- coding: utf-8 -*-
import asyncio
from functools import partial
from typing import Callable

from app.core.logger import tdump_log


class AsyncTaskQueue:
    def __init__(self, max_size: int = 10000):
        self.queue = asyncio.Queue(maxsize=max_size)
        self.worker_task = None
        self.running = False

    async def start(self):
        self.running = True
        self.worker_task = asyncio.create_task(self._task_worker())

    async def stop(self):
        self.running = False
        if not self.worker_task:
            return

        await self.queue.join()
        self.worker_task.cancel()
        try:
            await self.worker_task
        except asyncio.CancelledError:
            pass

    async def put_task(self, task: Callable, *args, **kwargs):
        """
        task:
        :param task:方法名称
        :return:
        """
        try:
            task_dict = {'func': task, 'args': args, 'kwargs': kwargs}
            await self.queue.put(task_dict)
            # 非阻塞方式放入队列，如果队列满则丢弃
            # self.queue.put_nowait(log_data)
        except asyncio.QueueFull:
            # 队列满时丢弃日志，避免阻塞主流程
            tdump_log.warning('日志队列已满，丢弃日志数据')

    async def _task_worker(self):
        while self.running:
            try:
                task_item = await self.queue.get()
                try:
                    func = task_item['func']
                    args = task_item['args']
                    kwargs = task_item['kwargs']
                    # 执行任务
                    await partial(func, *args, **kwargs)()

                except Exception as e:
                    print(f'任务执行失败: {e}')
                finally:
                    # 标记任务完成
                    self.queue.task_done()
            except Exception as e:
                print(f'任务协程异常: {e}')
                await asyncio.sleep(0.1)  # 避免忙等待


async_task_queue = AsyncTaskQueue()


# 应用启动时启动日志队列
async def start_async_task_queue():
    await async_task_queue.start()


# 应用关闭时停止日志队列
async def stop_async_task_queue():
    await async_task_queue.stop()
