import asyncio
import contextlib
from functools import lru_cache, partial
import queue

from odoo_rpc_client import Client

from app.core.config import settings
from app.core.exceptions.exception import TdumpException
from app.core.logger import tdump_log


@lru_cache
def init_odoo_client_with_config():
    odoo_config = settings.MSF_ODOO_CONFIG
    msf_client_config = {}
    for region_code, client_config in odoo_config.items():
        enable = client_config.get('enable', False)
        if not enable:
            continue
        host = client_config.get('host', False)
        port = client_config.get('port', False)
        dbname = client_config.get('dbname', False)
        user = client_config.get('user', False)
        pwd = client_config.get('pwd', False)

        if not all([host, port, dbname, user, pwd]):
            raise Exception('请在配置文件中配置odoo服务器连接信息')

        msf_client_config[region_code] = {'host': host, 'dbname': dbname, 'user': user, 'pwd': pwd, 'port': port}
    return msf_client_config


client_config_dict = init_odoo_client_with_config()


class ClientPool:
    def __init__(self, max_size=20):
        # 使用队列来管理对象池
        self._pool_dict = {}
        self._max_size = max_size

    def acquire(self, region_code):
        if region_code not in client_config_dict:
            raise TdumpException(f'区县{region_code}未上线PBA')

        pool = self._pool_dict.setdefault(region_code, {'pool': queue.Queue(self._max_size), 'current_size': 0})

        try:
            # 如果池中有可用的对象，则获取
            return pool['pool'].get(block=False)
        except queue.Empty:
            # 如果池为空，并且未达到最大对象数，则创建新的对象
            if pool['current_size'] < self._max_size:
                pool['current_size'] += 1
                client = Client(**client_config_dict[region_code], protocol='json-rpc')
                return client
            else:
                # 如果达到最大对象数且无可用对象，则阻塞等待
                return pool['pool'].get(block=True, timeout=3)

    def release(self, obj, region_code):
        """
        将对象放回池中
        :param obj: 要释放的客户端对象
        :param region_code: 区域代码
        :return:
        """
        try:
            self._pool_dict[region_code]['pool'].put(obj, block=False)
        except queue.Full:
            # 如果队列已满，则减少当前对象计数并记录
            self._pool_dict[region_code]['current_size'] -= 1
            tdump_log.debug(f'池已满，关闭{region_code}的odoo客户端对象')
            # 可选：尝试关闭连接以释放资源
            try:
                if hasattr(obj, 'close'):
                    obj.close()
            except Exception:
                pass  # 忽略关闭时的异常



client_pool = ClientPool()


@contextlib.contextmanager
def find_odoo_client(regin_code):
    odoo_client_config = client_config_dict.get(str(regin_code), None)
    if not odoo_client_config:
        raise TdumpException(f'区县{regin_code}未上线维修资金API')
    client = client_pool.acquire(regin_code)
    try:
        yield client
    finally:
        client_pool.release(client, regin_code)


class MsfRpc(object):
    def __init__(self, msf_model: str, region_code: str):
        self.msf_model = msf_model
        self.region_code = region_code

    async def call_method(self, method, *args, **kwargs):
        with find_odoo_client(self.region_code) as client:
            loop = asyncio.get_event_loop()
            try:
                # body = {'uid': call_id,
                #         'client_ip': get_client_ip(request),
                #         'url': request.url.path,
                #         'data': req['data'],
                #         'req': req,
                #         }

                # body = dict_to_json(body, ensure_ascii=False, indent=4)
                ret = await loop.run_in_executor(
                    None, partial(client.execute, self.msf_model, method, 0, *args, *kwargs)
                )
                return ret

            except Exception as e:
                msg = f'[{self.region_code}][{self.msf_model}][{method}]发生未知错误{e}'
                tdump_log.error(msg)
                raise TdumpException(message=msg) from e
