# -*- coding: utf-8 -*-
import json
import time
from typing import Any, Dict, List, Optional, Union

from fastapi import status
from fastapi.responses import JSONResponse

from app.core.exceptions.exception import ExceptionEnum
from app.utils.json_helper import CsJsonEncoder


class ApiResponse(JSONResponse):
    """
    统一响应格式
    success: 是否成功
    status_code: 响应状态码
    code: 响应编码
    detail: 响应内容
    message: 响应消息提示
    """

    status_code: int = status.HTTP_200_OK
    code: Union[int, str] = 0
    success: bool = True
    msg: str = '成功'
    timestamp: int = int(time.time() * 1000)
    data: Union[List[Any], Dict[str, Any]] = None

    def __init__(
        self,
        *,
        code: Union[int, str] = None,
        success: bool = None,
        msg: str = None,
        data: Union[List[Any], Dict[str, Any]] = None,
        status_code: int = None,
        **kwargs,
    ):
        """

        :param success: 是否成功
        :param status_code: 响应状态码
        :param code: 响应编码
        :param data: 响应内容
        :param msg: 响应消息提示
        :param kwargs: 其他的参数
        """
        self.msg = msg or self.msg
        self.code = code or self.code
        self.success = success or self.success
        self.status_code = status_code or self.status_code
        self.data = data

        try:
            code = int(self.code)
        except Exception:
            code = self.code
        body = {
            'code': code,
            'msg': self.msg,
            'data': self.data,
            'success': self.success,
            'timestamp': self.timestamp,
        }
        body.update(kwargs)
        super(ApiResponse, self).__init__(status_code=self.status_code, content=body, **kwargs)

    def render(self, content: Any) -> bytes:
        return json.dumps(
            content, ensure_ascii=False, allow_nan=False, indent=None, separators=(',', ':'), cls=CsJsonEncoder
        ).encode('utf-8')


class SuccessResp(ApiResponse):
    status_code: int = status.HTTP_200_OK
    code: Union[int, str] = ExceptionEnum.SUCCESS.value[0]
    msg: str = ExceptionEnum.SUCCESS.value[1]
    success: bool = True


class FailResp(ApiResponse):
    status_code: int = status.HTTP_200_OK
    code: Union[int, str] = ExceptionEnum.FAILED.value[0]
    msg: str = ExceptionEnum.FAILED.value[1]
    success: bool = False


class PaginationResp(SuccessResp):
    total: int = 0
    page: int = 1
    page_size: int = 20
    data: List = []

    def __init__(
        self,
        *,
        code: Union[int, str] = None,
        success: bool = None,
        msg: str = None,
        data: Optional[Any] = None,
        status_code: int = None,
        total: int,
        page: int,
        page_size: int,
        **kwargs,
    ):
        self.msg = msg or self.msg
        self.code = code or self.code
        self.success = success or self.success
        self.status_code = status_code or self.status_code
        self.data = data
        self.total = total
        self.page = page
        self.page_size = page_size
        try:
            code = int(self.code)
        except Exception:
            code = self.code

        # todo  diaoyc 感觉统一放入到data中比较合适, 但是需要修改前端的逻辑，暂时先这样子吧

        body = {
            'code': code,
            'msg': self.msg,
            'data': self.data,
            'total': total,
            'page': page,
            'page_size': page_size,
            'success': self.success,
            'timestamp': self.timestamp,
        }
        body.update(kwargs)

        super(ApiResponse, self).__init__(status_code=self.status_code, content=body, **kwargs)
