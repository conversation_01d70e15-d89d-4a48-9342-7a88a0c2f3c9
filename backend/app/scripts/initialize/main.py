# -*- coding: utf-8 -*-
import json
from pathlib import Path
import shutil
from typing import Dict, List

from aerich import Command
from tortoise.expressions import Q

from app.core.config import settings
from app.core.logger import tdump_log
from app.crud.system.api_crud import api_dao
from app.crud.system.menu_crud import menu_dao
from app.crud.system.user_crud import user_dao
from app.models import Api, Menu, Role
from app.schemas.system.user_schema import UserCreate
from app.services.system.api_service import ApiService
from app.services.system.user_service import UserService


class InitializeData:
    """
    初始化数据
    """

    SCRIPT_DIR: Path = Path.joinpath(settings.APP_BASE_DIR, 'scripts', 'initialize')
    DATA_DIR: Path = Path.joinpath(SCRIPT_DIR, 'data')

    @staticmethod
    async def init_db():
        command = Command(tortoise_config=settings.TORTOISE_ORM)
        try:
            await command.init_db(safe=True)
        except FileExistsError:
            pass

        await command.init()  # 初始化命令
        try:
            await command.migrate()  # 生成迁移文件
        except AttributeError:
            tdump_log.warning(
                'unable to retrieve model history from database, model history will be created from scratch'
            )
            shutil.rmtree('migrations')
            await command.init_db(safe=True)

        await command.upgrade(run_in_transaction=True)  # 应用迁移

    async def init_superuser(self):
        user = await user_dao.model.exists()
        if not user:
            # 从JSON文件读取用户数据
            users_data = self.read_init_from_json('users.json')
            for user_data in users_data:
                await UserService.create_user(UserCreate(**user_data))

    async def init_menus(self):
        menus = await menu_dao.model.exists()
        if not menus:
            # 从JSON文件读取菜单数据
            menus_data = self.read_init_from_json('menus.json')

            # 创建父级菜单和子菜单
            for menu_data in menus_data:
                children_data = menu_data.pop('children', [])

                # 创建父菜单
                parent_menu = await Menu.create(**menu_data)

                # 创建子菜单
                if children_data:
                    for child_data in children_data:
                        child_data['parent_id'] = parent_menu.id
                        await Menu.create(**child_data)

    @staticmethod
    async def init_apis():
        apis = await api_dao.model.exists()
        if not apis:
            await ApiService.refresh_api()

    async def init_roles(self):
        roles = await Role.exists()
        if not roles:
            # 从JSON文件读取角色数据
            roles_data = self.read_init_from_json('roles.json')
            admin_role = None
            user_role = None

            # 创建角色
            for role_data in roles_data:
                is_admin = role_data.pop('is_admin', False)
                role = await Role.create(**role_data)

                if is_admin:
                    admin_role = role
                else:
                    user_role = role

            # 分配权限
            if admin_role:
                # 分配所有API给管理员角色
                all_apis = await Api.all()
                await admin_role.apis.add(*all_apis)

                # 分配所有菜单给管理员
                all_menus = await Menu.all()
                await admin_role.menus.add(*all_menus)

            if user_role:
                # 分配所有菜单给普通用户
                all_menus = await Menu.all()
                await user_role.menus.add(*all_menus)

                # 为普通用户分配基本API
                basic_apis = await Api.filter(Q(method__in=['GET']) | Q(tags='基础模块'))
                # 默认添加修改密码的权限
                api_modify_pwd = await Api.filter(Q(summary='修改密码'))
                await user_role.apis.add(*basic_apis, *api_modify_pwd)

    def read_init_from_json(self, filename) -> List[Dict]:
        """
        从JSON文件读取初始化数据
        :param filename: JSON文件名
        :return: 数据列表
        """
        file_path = Path.joinpath(self.DATA_DIR, filename)
        if not file_path.exists():
            return []

        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)

    async def init_data(self):
        await self.init_db()
        await self.init_superuser()
        await self.init_menus()
        await self.init_apis()
        await self.init_roles()

    def __init__(self) -> None:
        pass

    async def run(self) -> None:
        """
        执行初始化操作
        :return:
        """
        await self.init_data()
