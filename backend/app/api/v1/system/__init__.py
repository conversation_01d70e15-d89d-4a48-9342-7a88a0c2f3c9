# -*- coding: utf-8 -*-
from fastapi import APIRouter

from app.core.dependency import DependPermission

from .apis import router as apis_router
from .auditlog import router as auditlog_router
from .auth import router as auth_router
from .depts import router as depts_router
from .menus import router as menus_router
from .roles import router as roles_router
from .user import router as user_router


SystemRouter = APIRouter()
SystemRouter.include_router(auth_router, prefix='/base', tags=['基础模块'])
SystemRouter.include_router(apis_router, prefix='/api', tags=['API模块'], dependencies=[DependPermission])
SystemRouter.include_router(auditlog_router, prefix='/auditlog', tags=['审计日志模块'], dependencies=[DependPermission])
SystemRouter.include_router(depts_router, prefix='/dept', tags=['部门模块'], dependencies=[DependPermission])
SystemRouter.include_router(menus_router, prefix='/menu', tags=['菜单模块'], dependencies=[DependPermission])
SystemRouter.include_router(roles_router, prefix='/role', tags=['角色模块'], dependencies=[DependPermission])
SystemRouter.include_router(user_router, prefix='/user', tags=['用户模块'], dependencies=[DependPermission])

__all__ = ['SystemRouter']
