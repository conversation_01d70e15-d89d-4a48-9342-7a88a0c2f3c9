# -*- coding: utf-8 -*-
from datetime import <PERSON><PERSON><PERSON>
import uuid

from fastapi import APIRouter, Depends, Request
from starlette.responses import JSONResponse

from app.core.config import settings
from app.core.exceptions.exception import SmsException
from app.core.response import SuccessResp
from app.core.security import CustomOAuth2PasswordRequestForm
from app.enums.redis_enum import RedisKeyEnum
from app.models.system.admin import User
from app.schemas.system.auth_schema import CaptchaOut, JwtOut, RefreshTokenPayload, SmsPayload
from app.schemas.system.user_schema import UserRegister, UserForgetPwd
from app.services.system.auth_service import CaptchaService, LoginService
from app.services.system.sms_servicce import SmsService
from app.services.system.user_service import UserService


router = APIRouter()


# @router.post('/access_token', response_model=JwtOut, summary='获取token', description='获取Token')
# async def login_access_token(request: Request,
#                              credentials: CredentialsSchema):
#     user: User = await LoginService.authenticate_user(request=request, credentials=credentials)
#     session_id = str(uuid.uuid4())
#     login_token = await LoginService.create_token(user, session_id=session_id)
#     # 如果已经登录了，再次登录则原来的token失效，
#     # 如果允许同个账号多次登录，只需要使用uuid作为唯一key，如果不允许的话，就用当前用户ID作为key,这样每次登录的时候原来的token就会失效
#     access_redis_key = user.id
#     if settings.APP_SAME_TIME_LOGIN:
#         access_redis_key = session_id
#
#     access_token_key = RedisKeyEnum.ACCESS_TOKEN.key.format(key=access_redis_key)
#     await request.app.state.redis.set(access_token_key, login_token.access_token,
#                                       ex=timedelta(minutes=settings.JWT_ACCESS_TOKEN_EXPIRE_MINUTES))
#
#     # 如果是从API文档中来的，需要直接
#     if settings.APP_DOCS_URL in request.headers.get('referer', ''):
#         return login_token.model_dump()
#
#     return SuccessResp(data=login_token.model_dump(), msg='登录成功')


@router.post('/access_token', response_model=JwtOut, summary='获取token', description='获取Token')
async def login_access_token(request: Request, credentials: CustomOAuth2PasswordRequestForm = Depends()):
    await LoginService.check_captcha(request=request, credentials=credentials)
    user: User = await LoginService.authenticate_user(request=request, credentials=credentials)
    session_id = str(uuid.uuid4())
    login_token = await LoginService.create_token(user, session_id=session_id)
    # 如果已经登录了，再次登录则原来的token失效，
    # 如果允许同个账号多次登录，只需要使用uuid作为唯一key，如果不允许的话，就用当前用户ID作为key,这样每次登录的时候原来的token就会失效
    access_redis_key = user.id
    if settings.APP_SAME_TIME_LOGIN:
        access_redis_key = session_id

    access_token_key = RedisKeyEnum.ACCESS_TOKEN.key.format(key=access_redis_key)
    await request.app.state.redis.set(
        access_token_key, login_token.access_token, ex=timedelta(minutes=settings.JWT_ACCESS_TOKEN_EXPIRE_MINUTES)
    )

    # 如果是从API文档中来的，需要直接
    if settings.APP_DOCS_URL in request.headers.get('referer', ''):
        return login_token.model_dump()

    return SuccessResp(data=login_token.model_dump(), msg='登录成功')


@router.post('/refresh/token', response_model=JwtOut, summary='刷新token', description='刷新token')
async def refresh_token(payload: RefreshTokenPayload) -> JSONResponse:
    new_token = await LoginService.refresh_token(payload.refresh_token)
    return SuccessResp(data=new_token.model_dump())


@router.get('/captcha', response_model=CaptchaOut, summary='获取验证码', description='获取登录验证码')
async def get_captcha(request: Request) -> JSONResponse:
    captcha = await CaptchaService.get_captcha(request)
    return SuccessResp(data=captcha)


@router.post('/register', summary='用户注册', description='注册用户')
async def do_register(request: Request, user_register: UserRegister) -> JSONResponse:
    # 尝试获取biz
    biz_no = await SmsService.get_biz_no_from_redis(request=request, phone=user_register.phone)
    # 验证短信验证码是否正确
    verify_res = await SmsService.verity_code(phone=user_register.phone, biz_no=biz_no, code=user_register.code)
    if not verify_res:
        raise SmsException()
    # 创建用户
    user_data = await UserService.create_user(user_register)
    return SuccessResp(data=user_data)


@router.post('/sms_send', summary='发送手机验证码', description='发送手机验证码')
async def send_sms(request: Request, user_in: SmsPayload) -> JSONResponse:
    # 验证验证码
    await LoginService.check_captcha(request=request, credentials=user_in)
    # 储存biz
    biz_no = await SmsService.get_set_biz_no_to_redis(request=request, phone=user_in.phone)
    # 发送短信
    await SmsService.send_code(user_in.phone, biz_no)
    return SuccessResp(msg='发送成功')


@router.post('/forget_password', summary='用户忘记密码', description='用户忘记密码')
async def do_forget_pwd(request: Request, user_register: UserForgetPwd) -> JSONResponse:
    # 尝试获取biz
    biz_no = await SmsService.get_biz_no_from_redis(request=request, phone=user_register.phone)
    # 验证短信验证码是否正确
    verify_res = await SmsService.verity_code(phone=user_register.phone, biz_no=biz_no, code=user_register.code)
    if not verify_res:
        raise SmsException()
    # 更改密码
    user_data = await UserService.forget_password(user_register.phone, user_register.password)
    return SuccessResp(msg='重置成功')
