# -*- coding: utf-8 -*-
from typing import Annotated

from fastapi import APIRouter, Depends

from app.api.v1.params import AuditQueryParams, PaginationQueryParams
from app.core.response import PaginationResp
from app.services.system.audit_log_service import AuditLogServices


router = APIRouter()


@router.get('/list', summary='查看操作日志')
async def get_audit_log_list(
    page_query: Annotated[PaginationQueryParams, Depends()],
    audit_log_query: Annotated[AuditQueryParams, Depends()],
):
    total, data = await AuditLogServices.get_audit_log_list(page_query, audit_log_query)
    return PaginationResp(data=data, total=total, page=page_query.page, page_size=page_query.page_size)
