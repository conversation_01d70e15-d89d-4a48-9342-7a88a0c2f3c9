# -*- coding: utf-8 -*-
from typing import Annotated

from fastapi import APIRouter, Depends, Query

from app.api.v1.params import ApiQueryParams, PaginationQueryParams
from app.core.response import PaginationResp, SuccessResp
from app.schemas.system.api_schema import ApiCreate, ApiUpdate
from app.services.system.api_service import ApiService


router = APIRouter()


@router.get('/list', summary='查看API列表')
async def list_api(
    page_query: Annotated[PaginationQueryParams, Depends()],
    api_query: Annotated[ApiQueryParams, Depends()],
):
    total, data = await ApiService.get_api_list(page_query, api_query)
    return PaginationResp(data=data, total=total, page=page_query.page, page_size=page_query.page_size)


@router.get('/get', summary='查看Api')
async def get_api(api_id: int = Query(..., description='Api')):
    data = await ApiService.get_api_detail(api_id=api_id)
    return SuccessResp(data=data)


@router.post('/create', summary='创建Api')
async def create_api(
    api_in: ApiCreate,
):
    data = await ApiService.create_api(api_in)
    return SuccessResp(data=data, msg='创建成功')


@router.post('/update', summary='更新Api')
async def update_api(api_in: ApiUpdate):
    data = await ApiService.update_api(api_in)
    return SuccessResp(data=data, msg='更新成功')


@router.delete('/delete', summary='删除Api')
async def delete_api(
    api_id: int = Query(..., description='ApiID'),
):
    await ApiService.remove_api(api_id)
    return SuccessResp(msg='删除成功')


@router.post('/refresh', summary='刷新API列表')
async def refresh_api():
    await ApiService.refresh_api()
    return SuccessResp(msg='刷新成功')
