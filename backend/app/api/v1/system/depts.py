from fastapi import APIRouter, Query

from app.core.response import SuccessResp
from app.schemas.system.depts_schema import DeptCreate, DeptUpdate
from app.services.system.dept_service import DeptService


router = APIRouter()


@router.get('/list', summary='查看部门列表')
async def list_dept(name: str = Query(None, description='部门名称')):
    dept_tree = await DeptService.get_dept_tree(name)
    return SuccessResp(data=dept_tree)


@router.get('/get', summary='查看部门')
async def get_dept(dept_id: int = Query(..., description='部门ID')):
    data = await DeptService.get_dept_detail(dept_id)
    return SuccessResp(data=data)


@router.post('/create', summary='创建部门')
async def create_dept(dept_in: DeptCreate):
    await DeptService.create_dept(obj_in=dept_in)
    return SuccessResp(msg='创建成功')


@router.post('/update', summary='更新部门')
async def update_dept(dept_in: DeptUpdate):
    await DeptService.update_dept(obj_in=dept_in)
    return SuccessResp(msg='更新成功')


@router.delete('/delete', summary='删除部门')
async def delete_dept(dept_id: int = Query(..., description='部门ID')):
    await DeptService.delete_dept(dept_id=dept_id)
    return SuccessResp(msg='删除成功')
