# -*- coding: utf-8 -*-
from typing import Annotated

from fastapi import APIRouter, Depends, Query

from app.api.v1.params import PaginationQueryParams, RoleQueryParams
from app.core.response import PaginationResp, SuccessResp
from app.schemas.system.role_schema import RoleC<PERSON>, RoleUpdate, RoleUpdateMenusApis
from app.services.system.role_service import RoleService


router = APIRouter()


@router.get('/list', summary='查看角色列表')
async def list_role(
    page_query: Annotated[PaginationQueryParams, Depends()], role_query: Annotated[RoleQueryParams, Depends()]
):
    total, data = await RoleService.get_role_list(page_query, role_query)
    return PaginationResp(data=data, total=total, page=page_query.page, page_size=page_query.page_size)


@router.get('/get', summary='查看角色')
async def get_role(
    role_id: int = Query(..., description='角色ID'),
):
    data = await RoleService.get_role_detail(role_id)
    return SuccessResp(data=data, msg='查询成功')


@router.post('/create', summary='创建角色')
async def create_role(role_in: RoleCreate):
    data = await RoleService.create_role(role_in)
    return SuccessResp(data=data, msg='创建角色成功')


@router.post('/update', summary='更新角色')
async def update_role(role_in: RoleUpdate):
    data = await RoleService.update_role(role_in.id, role_in=role_in)
    return SuccessResp(data=data, msg='更新角色成功')


@router.delete('/delete', summary='删除角色')
async def delete_role(
    role_id: int = Query(..., description='角色ID'),
):
    await RoleService.remove_role(role_id)
    return SuccessResp(msg='删除角色成功')


@router.get('/authorized', summary='查看角色权限')
async def get_role_authorized(role_id: int = Query(..., description='角色ID')):
    data = await RoleService.view_role_authorized(role_id)
    return SuccessResp(data=data, msg='查看角色成功')


@router.post('/authorized', summary='更新角色权限')
async def update_role_authorized(role_in: RoleUpdateMenusApis):
    await RoleService.update_role_authorized(role_id=role_in.id, role_in=role_in)
    return SuccessResp(msg='更新角色成功')
