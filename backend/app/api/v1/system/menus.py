# -*- coding: utf-8 -*-
from typing import Annotated

from fastapi import APIRouter, Depends, Query

from app.api.v1.params import PaginationQueryParams
from app.core.response import PaginationResp, SuccessResp
from app.schemas.system.menu_schema import MenuCreate, MenuUpdate
from app.services.system.menu_service import MenuService


router = APIRouter()


@router.get('/list', summary='查看菜单列表')
async def list_menu(page_query: Annotated[PaginationQueryParams, Depends()]):
    total, data = await MenuService.get_menu_list(page_query)
    return PaginationResp(data=data, total=total, page=page_query.page, page_size=page_query.page_size)


@router.get('/get', summary='查看菜单')
async def get_menu(
    menu_id: int = Query(..., description='菜单id'),
):
    data = await MenuService.get_menu_detail(menu_id)
    return SuccessResp(data=data, msg='查看菜单详情成功')


@router.post('/create', summary='创建菜单')
async def create_menu(menu_in: MenuCreate):
    data = await MenuService.create_menu(menu_in)
    return SuccessResp(data=data)


@router.post('/update', summary='更新菜单')
async def update_menu(
    menu_in: MenuUpdate,
):
    data = await MenuService.update_menu(menu_in.id, menu_in)
    return SuccessResp(data=data)


@router.delete('/delete', summary='删除菜单')
async def delete_menu(menu_id: int = Query(..., description='菜单id')):
    await MenuService.remove_menu(menu_id)
    return SuccessResp(msg='删除成功')
