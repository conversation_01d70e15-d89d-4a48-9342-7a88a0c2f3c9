# -*- coding: utf-8 -*-
from typing import Annotated

from fastapi import APIRouter, Body, Depends, Query

from app.api.v1.params import PaginationQueryParams, UserQueryParams
from app.core.context import CTX_USER_ID
from app.core.dependency import DependAuth
from app.core.response import PaginationResp, SuccessResp
from app.schemas.system.user_schema import UpdatePassword, UserCreate, UserUpdate
from app.services.system.user_service import UserService


router = APIRouter()


# todo diaoyc:返回的用户的敏感信息是否需要处理？如手机号


@router.get('/userinfo', summary='查看用户信息', dependencies=[DependAuth])
async def get_userinfo():
    user_id = CTX_USER_ID.get()
    data = await UserService.get_current_user_info(user_id)
    return SuccessResp(data=data)


@router.get('/usermenu', summary='查看用户菜单', dependencies=[DependAuth])
async def get_user_menu():
    user_id = CTX_USER_ID.get()
    data = await UserService.get_user_menu(user_id)
    return SuccessResp(data=data)


@router.get('/userapi', summary='查看用户API', dependencies=[DependAuth])
async def get_user_api():
    user_id = CTX_USER_ID.get()
    data = await UserService.get_user_api(user_id)
    return SuccessResp(data=data)


@router.post('/update_password', summary='修改密码', dependencies=[DependAuth])
async def update_user_password(payload: UpdatePassword):
    user_id = CTX_USER_ID.get()
    await UserService.update_user_password(user_id, payload)
    return SuccessResp(msg='修改成功')


@router.get('/list', summary='查看用户列表', description='查看用户列表')
async def list_user(
    page_query: Annotated[PaginationQueryParams, Depends()],
    user_query: Annotated[UserQueryParams, Depends()],
):
    total, data = await UserService.get_user_list(page_query, user_query)
    return PaginationResp(data=data, total=total, page=page_query.page, page_size=page_query.page_size)


@router.get('/get', summary='查看用户', description='查看用户')
async def get_user(user_id: int = Query(..., description='用户ID')):
    return SuccessResp(data=await UserService.get_user_info(user_id))


@router.post('/create', summary='创建用户')
async def create_user(user_in: UserCreate):
    data = await UserService.create_user(user_in)
    return SuccessResp(data=data, msg='创建成功')


@router.post('/update', summary='更新用户')
async def update_user(user_in: UserUpdate):
    data = await UserService.update_user(user_in)
    return SuccessResp(data=data, msg='更新用户成功')


@router.delete('/delete', summary='删除用户')
async def delete_user(user_id: int = Query(..., description='用户ID')):
    await UserService.remove_user(user_id)
    return SuccessResp(msg='删除用户成功')


@router.post('/reset_password', summary='重置密码')
async def reset_password(user_id: int = Body(..., description='用户ID', embed=True)):
    await UserService.reset_password(user_id)
    return SuccessResp(msg='密码已重置为123456')
