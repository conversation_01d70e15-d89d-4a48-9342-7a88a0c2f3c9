# -*- coding: utf-8 -*-

from fastapi import APIRouter
from app.core.response import SuccessResp
from app.schemas.pba.pba_schema import FilingDetail
from app.services.pba.odoo_service import PbaOdooService

router = APIRouter()


@router.post('/rentFilingDetail/{region_code}', summary='查询备案凭证信息', description='查询备案凭证信息')
async def query_filing(region_code: str, filing_data: FilingDetail):

    data = await PbaOdooService.query_filing(region_code, filing_data)
    return SuccessResp(data=data)


@router.post(
    '/RentContractDetail/{region_code}/{contract_no}',
    summary='查询合同的基础信息',
    description='查询合同的基础信息，提供给扫描合同上二维码使用',
)
async def view_contract_by_public(region_code: str, contract_no: str):
    data = await PbaOdooService.view_contract_by_public(region_code, contract_no)
    return SuccessResp(data=data)
