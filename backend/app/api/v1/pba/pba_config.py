# -*- coding: utf-8 -*-

from fastapi import APIRouter, Request

from app.core.dependency import DependAuth
from app.core.response import SuccessResp
from app.services.pba.config_service import PbaConfigService


router = APIRouter()


@router.get('/regions', summary='查看区域列表', description='查看区域列表')
def get_all_region():
    return SuccessResp(data=PbaConfigService.get_all_regions())


@router.get('/{region_code}', summary='获取所有基础配置', description='获取所有基础配置',
            dependencies=[DependAuth])
async def get_all_config(request: Request, region_code):
    data = await PbaConfigService.get_all_config(request, region_code=region_code)
    # 自定义数据过滤
    _tmp_ownerships_type = [k for k in data['ownerships_type']['data'] if k['code'] in ['1', '3']]
    data['ownerships_type']['data'][0].update({'name': '不动产权证/房屋所有权证/其他'})
    data['ownerships_type']['data'] = _tmp_ownerships_type
    data['rent_type']['data'] = [k for k in data['rent_type']['data'] if k['code'] not in ['other']]

    filtered_idtype = []
    for k in data['idtype']['data']:
        if k['code'] in ['1']:
            k['name'] = '身份证'
            filtered_idtype = [k]
            break
    data['idtype']['data'] = filtered_idtype
    return SuccessResp(data=data)
