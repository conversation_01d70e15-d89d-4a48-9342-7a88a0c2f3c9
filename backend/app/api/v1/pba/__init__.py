from fastapi import APIRouter

from app.core.dependency import DependAuth, DependPermission

from .pba_common import router as common_router
from .pba_config import router as config_router
from .pba_contract import router as contract_router
from .pba_sign import router as pba_sign


PbaRouter = APIRouter()
PbaRouter.include_router(config_router, prefix='/pba/config', tags=['pba基础配置'])
PbaRouter.include_router(common_router, prefix='/pba/common', tags=['pba公开的接口'])
PbaRouter.include_router(contract_router, prefix='/pba/contract', tags=['pba合同'], dependencies=[DependAuth])
PbaRouter.include_router(pba_sign, prefix='/pba/signing', tags=['pba合同签约'], dependencies=[DependAuth])


__all__ = ['PbaRouter']
