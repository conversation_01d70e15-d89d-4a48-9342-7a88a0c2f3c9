# -*- coding: utf-8 -*-

from fastapi import APIRouter

from app.core.response import SuccessResp
from app.services.pba.odoo_service import PbaOdooService


router = APIRouter()


@router.post('/{region_code}/{contract_no}', summary='进入签约流程', description='进入签约流程')
async def start_signing_process(region_code:str, contract_no:str, redirect_url:str):
    """
    开始签约
    :param region_code:区划代码
    :param contract_no: 合同编码
    :param redirect_url: 查询参数：电子前面回调的URL
    :return:
    """
    data = await PbaOdooService.start_signing_process(region_code, contract_no, redirect_url)
    return SuccessResp(data=data)

@router.get('/{region_code}/{contract_no}', summary='获取合同签约进度', description='获取合同签约进度')
async def get_contract_signing_process(region_code:str, contract_no:str):
    data = await PbaOdooService.get_contract_signing_process(region_code, contract_no)
    return SuccessResp(data=data)
