# -*- coding: utf-8 -*-
import base64
from datetime import datetime
from typing import Annotated

from fastapi import APIRouter, Depends, Response

from app.api.v1.params import ContractQueryParams, PaginationQueryParams
from app.core.context import CTX_USER_ID
from app.core.response import SuccessResp
from app.schemas.pba.pba_schema import ContractCancel, ContractCreate, ContractUpdate, VerifyPayload
from app.services.pba.odoo_service import PbaOdooService
from app.services.system.user_service import UserService


router = APIRouter()


# todo 返回的数据中，存在手机号、证件号码、需要做掩码处理？ 但是提交的时候如何知道手机号码呢？
#  手机号掩码已经有方法，还需要实现证件类型的掩码方法


@router.get('/{region_code}', summary='查看合同列表', description='查看合同列表')
async def get_contract_list(
        region_code: str,
        page_query: Annotated[PaginationQueryParams, Depends()],
        contract_query: Annotated[ContractQueryParams, Depends()],
):
    user_info = await UserService.get_current_user_info(CTX_USER_ID.get())
    # todo 可能会失败？
    contract_query.phone = user_info.get('phone')
    data = await PbaOdooService.get_contract_list(region_code, page_query, contract_query)
    return SuccessResp(data=data)


@router.get('/{region_code}/{contract_no}', summary='查看合同详情', description='查看合同详情')
async def get_contract_detail(region_code, contract_no):
    data = await PbaOdooService.get_contract_detail(region_code, contract_no)
    return SuccessResp(data=data)


@router.get('/pdf/{region_code}/{contract_no}/filing', summary='查看备案证明PDF', description='查看备案证明PDF')
async def get_contract_filing_pdf(region_code, contract_no):
    data = await PbaOdooService.get_contract_filing_pdf(region_code, contract_no)
    pdf_content = base64.b64decode(data['content'])
    filename = data.get('name', 'temp')
    return Response(content=pdf_content, media_type='application/pdf', headers={
        'Content-Disposition': f'inline; filename="{filename}.pdf"'
    })


@router.post('/{region_code}', summary='保存合同', description='保存合同')
async def save_contract(region_code, contract: ContractCreate):
    user_info = await UserService.get_current_user_info(CTX_USER_ID.get())
    contract.drafter = user_info.get('phone')
    contract.drafting_time = datetime.now()
    data = await PbaOdooService.save_contract(region_code, contract)
    return SuccessResp(data=data)


@router.put('/{region_code}', summary='更新合同内容', description='更新合同内容')
async def update_contract(region_code, contract: ContractUpdate):
    user_info = await UserService.get_current_user_info(CTX_USER_ID.get())
    contract.drafter = user_info.get('phone')
    contract.drafting_time = datetime.now()
    data = await PbaOdooService.update_contract(region_code, contract)
    return SuccessResp(data=data)


@router.delete('/{region_code}/{contract_no}', summary='删除合同', description='删除合同')
async def delete_contract(region_code: str, contract_no: str):
    data = await PbaOdooService.delete_contract(region_code, contract_no)
    return SuccessResp(data=data)


@router.put('/cancel/{region_code}/{contract_no}', summary='取消合同', description='取消合同')
async def cancel_contract(region_code: str, contract_cancel: ContractCancel):
    data = await PbaOdooService.cancel_contract(region_code, contract_cancel)
    return SuccessResp(data=data)


@router.put('/revoke/{region_code}/{contract_no}', summary='撤销合同备案', description='撤销合同备案')
async def revoke_contract(region_code: str, contract_revoke: ContractCancel):
    data = await PbaOdooService.revoke_contract(region_code, contract_revoke)
    return SuccessResp(data=data)


@router.get('/pdf/{region_code}/{contract_no}/contract', summary='查看合同PDF', description='查看合同PDF')
async def get_contact_pdf(region_code, contract_no):
    data = await PbaOdooService.get_contact_pdf(region_code, contract_no)
    pdf_content = base64.b64decode(data['content'])
    filename = data.get('name', 'temp')
    return Response(content=pdf_content, media_type='application/pdf', headers={
        'Content-Disposition': f'inline; filename="{filename}.pdf"'
    })


@router.get('/pdf/{region_code}/{contract_no}/abstract', summary='查看合同摘要PDF', description='查看合同摘要PDF')
async def get_contract_abstract_pdf(region_code, contract_no):
    data = await PbaOdooService.get_contract_abstract_pdf(region_code, contract_no)
    pdf_content = base64.b64decode(data['content'])
    filename = data.get('name', 'temp')
    return Response(content=pdf_content, media_type='application/pdf', headers={
        'Content-Disposition': f'inline; filename="{filename}.pdf"'
    })


@router.put('/submit/{region_code}/{contract_no}', summary='提交合同', description='提交合同')
async def submit_contract(region_code: str, contract_no: str):
    data = await PbaOdooService.submit_contract(region_code, contract_no)
    return SuccessResp(data=data)


@router.post('/verify_house/{region_code}', summary='验证房源', description='验证房源')
async def verify_house(region_code: str, verify_data: VerifyPayload):
    data = await PbaOdooService.verify_house(region_code, verify_data)
    return SuccessResp(data=data)
