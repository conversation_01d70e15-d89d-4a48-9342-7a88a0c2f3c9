from datetime import datetime
from typing import Optional

from pydantic import BaseModel, Field


class PaginationQueryParams(BaseModel):
    model_config = {'extra': 'forbid'}

    page: int = Field(1, description='页码')
    page_size: int = Field(100, description='每页数量', ge=10, le=9999)
    # order_by: Literal['id', 'created_date', 'write_date'] = 'created_date'

    # tags: list[str] = []


class UserQueryParams(BaseModel):
    model_config = {'extra': 'forbid'}

    username: Optional[str] = Field(None, description='用户名称，用于搜索')
    email: Optional[str] = Field(None, description='邮箱地址')
    dept_id: Optional[int] = Field(None, description='部门ID')


class RoleQueryParams(BaseModel):
    model_config = {'extra': 'forbid'}

    role_name: Optional[str] = Field(None, description='角色名称，用于查询')


class ApiQueryParams(BaseModel):
    model_config = {'extra': 'forbid'}

    path: Optional[str] = Field(None, description='API路径')
    summary: Optional[str] = Field(None, description='API简介')
    tags: Optional[str] = Field(None, description='API模块')


class AuditQueryParams(BaseModel):
    model_config = {'extra': 'forbid'}

    username: Optional[str] = Field(None, description='操作人名称')
    module: Optional[str] = Field(None, description='功能模块')
    method: Optional[str] = Field(None, description='请求方法')
    summary: Optional[str] = Field(None, description='接口描述')
    path: Optional[str] = Field(None, description='请求路径')
    status: Optional[str] = Field(None, description='状态码')
    start_time: Optional[datetime] = Field(None, description='开始时间')
    end_time: Optional[datetime] = Field(None, description='结束时间')


class ContractQueryParams(BaseModel):
    model_config = {'extra': 'forbid'}

    phone: Optional[str] = Field(None, description='签约人手机号')
    contract_no: Optional[str] = Field(None, description='合同编号')
    state: Optional[str] = Field('', description='合同状态')


# class PaginationQueryParams:
#     def __init__(
#         self,
#         page: int = Query(1, description='页码'),
#         page_size: int = Query(10, description='每页数量', ge=100, le=1000),
#     ) -> None:
#         self.page = page
#         self.page_size = page_size


# class UserQueryParams:
#     def __init__(
#         self,
#         login: Optional[str] = Query(None, description='用户名'),
#         nickname: Optional[str] = Query(None, description='昵称'),
#         phone: Optional[str] = Query(None, description='手机号'),
#         email: Optional[str] = Query(None, description='邮箱'),
#         active: Optional[int] = Query(None, description='状态'),
#     ):
#         self.username = login
#         self.nickname = nickname
#         self.phone = phone
#         self.email = email
#         self.active = active
