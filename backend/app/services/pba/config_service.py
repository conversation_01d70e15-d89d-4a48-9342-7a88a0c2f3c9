# -*- coding: utf-8 -*-
from datetime import <PERSON><PERSON><PERSON>
import json
from typing import Dict
from urllib.request import Request

from app.core.config import settings
from app.core.logger import tdump_log
from app.enums.redis_enum import RedisKeyEnum
from app.services.pba.odoo_service import PbaOdooService


class PbaConfigService:
    @classmethod
    def get_all_regions(cls):
        regions = []
        for code, config in settings.MSF_ODOO_CONFIG.items():
            # 如果没有启用，则不返回给前端
            enable = config.get('enable', False)
            if not enable:
                continue
            regions.append({'name': config.get('name', ''), 'code': code, 'enable': enable})
        return regions

    @classmethod
    async def get_all_config(cls, request: Request, region_code: str) -> Dict:
        """
        获取所有的基础配置
        """
        config_data = await PbaOdooService.get_all_config_by_region_code(region_code)
        return config_data

        # app_redis = request.app.state.redis
        # pba_config_key = RedisKeyEnum.PBA_BASE_CONFIG.key.format(region_code=region_code)
        # try:
        #     # 尝试从Redis获取缓存数据
        #     cached_data = await app_redis.get(pba_config_key)
        #     if cached_data:
        #         # 反序列化缓存数据
        #         return json.loads(cached_data)
        # except Exception as e:
        #     # Redis读取异常，记录日志但不中断流程
        #     tdump_log.warning(f'Failed to get cache from Redis: {e}')
        #
        # try:
        #     # 从odoo服务获取数据
        #     config_data = await PbaOdooService.get_all_config_by_region_code(region_code)
        #     if config_data:
        #         try:
        #             # 序列化数据后存入Redis
        #             await app_redis.set(pba_config_key, json.dumps(config_data), ex=timedelta(minutes=24 * 60))
        #         except Exception as e:
        #             tdump_log.warning(f'Failed to set cache to Redis: {e}')
        #
        #     return config_data or {}
        #
        # except Exception as e:
        #     # 外部服务调用异常
        #     tdump_log.error(f'Failed to get config from PbaOdooService: {e}')
        #     return {}
