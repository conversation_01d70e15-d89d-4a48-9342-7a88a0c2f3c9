# -*- coding: utf-8 -*-
import traceback
from typing import Annotated, Dict

from fastapi import Depends

from app.api.v1.params import ContractQueryParams, PaginationQueryParams
from app.core.exceptions.exception import OdooRespException, TdumpException
from app.core.logger import tdump_log
from app.core.odoo_client import MsfRpc
from app.schemas.pba.pba_schema import ContractCreate, VerifyPayload, ContractUpdate, ContractCancel, FilingDetail
from app.utils.json_helper import dict_to_json, json_to_dict


class PbaOdooService:
    msf_model = 'msf.rhb_pba_public_method'

    @classmethod
    async def call_odoo_method(cls, region_code, method, *args, **kwargs):
        rpc = MsfRpc(msf_model=cls.msf_model, region_code=region_code)
        try:
            tdump_log.info(f'call {region_code} RPC Method {method}')
            response = await rpc.call_method(method, *args, **kwargs)
        except Exception as e:
            traceback.print_stack()
            tdump_log.error(f'内部rpc服务错误：{str(e)}')
            raise TdumpException(message='内部rpc服务错误') from e

        detail = response.get('detail')
        code = response.get('code')
        if code != 200:
            raise OdooRespException(code=code, message=response.get('message'))

        return detail

    @classmethod
    async def get_all_config_by_region_code(cls, region_code) -> Dict:
        config = await cls.call_odoo_method(region_code, method='get_all_codebook_config')
        return config

    @classmethod
    async def get_contract_list(
            cls,
            region_code: str,
            page_query: Annotated[PaginationQueryParams, Depends()],
            contract_query: Annotated[ContractQueryParams, Depends()],
    ) -> Dict:
        detail = await cls.call_odoo_method(
            region_code,
            'get_contract_list',
            contract_query.phone,
            page_query.page,
            page_query.page_size,
            contract_query.contract_no,
            contract_query.state.split(',') if contract_query.state else [],
        )
        return detail

    @classmethod
    async def get_contract_detail(cls, region_code, contract_no) -> Dict:
        detail = await cls.call_odoo_method(region_code, 'get_contract_detail', contract_no)
        return detail

    @classmethod
    async def get_contact_pdf(cls, region_code, contract_no) -> Dict:
        detail = await cls.call_odoo_method(region_code, 'get_contact_pdf', contract_no)
        return detail

    @classmethod
    async def get_contract_abstract_pdf(cls, region_code, contract_no) -> Dict:
        detail = await cls.call_odoo_method(region_code, 'get_contract_abstract_pdf', contract_no)
        return detail

    @classmethod
    async def get_contract_filing_pdf(cls, region_code, contract_no) -> Dict:
        detail = await cls.call_odoo_method(region_code, 'get_contract_filing_pdf', contract_no)
        return detail

    @classmethod
    async def save_contract(cls, region_code, contract: ContractCreate) -> Dict:
        detail = await cls.call_odoo_method(
            region_code, 'save_contract', json_to_dict(dict_to_json(contract.model_dump()))
        )
        return detail

    @classmethod
    async def update_contract(cls, region_code, contract: ContractUpdate) -> Dict:
        payload_dict = json_to_dict(dict_to_json(contract.model_dump()))
        detail = await cls.call_odoo_method(region_code, 'update_contract', payload_dict)
        return detail

    @classmethod
    async def submit_contract(cls, region_code, contract_no) -> Dict:
        detail = await cls.call_odoo_method(region_code, 'submit_contract', contract_no)
        return detail

    @classmethod
    async def delete_contract(cls, region_code, contract_no) -> Dict:
        detail = await cls.call_odoo_method(region_code, 'delete_contract', contract_no)
        return detail

    @classmethod
    async def start_signing_process(cls, region_code, contract_no, redirect_url) -> Dict:
        detail = await cls.call_odoo_method(region_code, 'start_signing_process', contract_no, redirect_url)
        return detail

    @classmethod
    async def get_contract_signing_process(cls, region_code, contract_no) -> Dict:
        detail = await cls.call_odoo_method(region_code, 'get_contract_signing_process', contract_no)
        return detail

    @classmethod
    async def cancel_contract(cls, region_code, contract_cancel: ContractCancel) -> Dict:
        detail = await cls.call_odoo_method(region_code, 'cancel_contract', contract_cancel.contract_no,
                                            contract_cancel.reason)
        return detail

    @classmethod
    async def revoke_contract(cls, region_code, contract_revoke: ContractCancel) -> Dict:
        detail = await cls.call_odoo_method(region_code, 'revoke_contract', contract_revoke.contract_no,
                                            contract_revoke.reason)
        return detail

    @classmethod
    async def verify_house(cls, region_code, verify_data: VerifyPayload) -> Dict:
        detail = await cls.call_odoo_method(region_code, 'verify_house',
                                            verify_data.ownership_no,
                                            verify_data.ownership_type_code,
                                            verify_data.owner_name,
                                            verify_data.owner_idcard_no,
                                            )
        return detail

    @classmethod
    async def query_filing(cls, region_code, filing_data: FilingDetail) -> Dict:
        detail = await cls.call_odoo_method(region_code, 'query_filing_certificate',
                                            filing_data.filing_no,
                                            filing_data.lessee_name,
                                            filing_data.id_number
                                            )
        return detail

    @classmethod
    async def view_contract_by_public(cls, region_code, contract_no) -> Dict:
        detail = await cls.call_odoo_method(region_code, 'view_contract_by_public', contract_no)
        return detail
