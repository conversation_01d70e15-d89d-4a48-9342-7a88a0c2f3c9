# -*- coding: utf-8 -*-
from typing import Dict, List

from tortoise.transactions import atomic

from app.crud.system.dept_crud import dept_dao
from app.models import Dept
from app.schemas.system.depts_schema import DeptCreate, DeptUpdate


class DeptService:
    @classmethod
    async def get_dept_tree(cls, name) -> List:
        all_dept_objs = await dept_dao.get_dept_by_name(name)

        # 辅助函数，用于递归构建部门树
        def build_tree(parent_id):
            return [
                {
                    'id': dept.id,
                    'name': dept.name,
                    'desc': dept.desc,
                    'order': dept.order,
                    'parent_id': dept.parent_id,
                    'children': build_tree(dept.id),  # 递归构建子部门
                }
                for dept in all_dept_objs
                if dept.parent_id == parent_id
            ]

        # 从顶级部门（parent_id=0）开始构建部门树
        dept_tree = build_tree(0)

        return dept_tree

    @classmethod
    async def get_dept_detail(cls, dept_id) -> Dict:
        dept_obj: Dept = await dept_dao.get_by_id(dept_id)
        return await dept_obj.to_dict()

    @classmethod
    @atomic()
    async def create_dept(cls, obj_in: DeptCreate):
        if obj_in.parent_id != 0:
            await dept_dao.get_by_id(dept_id=obj_in.parent_id)
        new_obj = await dept_dao.create(obj_in=obj_in)

        await dept_dao.update_dept_closure(new_obj)

    @classmethod
    @atomic()
    async def update_dept(cls, obj_in: DeptUpdate):
        dept_obj = await dept_dao.get_by_id(dept_id=obj_in.id)
        await dept_dao.update_dept_rel(dept_obj, obj_in.parent_id)
        await dept_dao.update_dept(dept_obj, obj_in)

    @classmethod
    @atomic()
    async def delete_dept(cls, dept_id: int):
        dept_dao.remove_dept(dept_id)
