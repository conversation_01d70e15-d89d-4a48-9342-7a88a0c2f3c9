# -*- coding: utf-8 -*-
import datetime
from datetime import timed<PERSON><PERSON>
from typing import NewType, Union

from fastapi import Request

from app.core.config import settings
from app.core.exceptions.exception import AccessException, LoginException, TdumpException
from app.core.logger import tdump_log
from app.core.security import (
    CustomOAuth2PasswordRequestForm,
    decode_access_token,
    generate_access_token,
    generate_hash_password,
    verify_hashed_password,
    verify_plain_password,
)
from app.crud.system.user_crud import user_dao
from app.enums.redis_enum import RedisKeyEnum
from app.models import User
from app.schemas.system.auth_schema import CredentialsSchema, JwtOut, JwtPayload
from app.utils.common import get_captcha_digits_and_ascii, get_random_character


CaptchaKey = NewType('CaptchaKey', str)
CaptchaBase64 = NewType('CaptchaBase64', str)


class LoginService:
    """
    登录模块
    """

    @classmethod
    async def check_captcha(cls, request, credentials):
        # 检查验证码是否正确
        # 判断是否开启验证码，开启则验证，否则不验证（dev模式下来自API文档的登录请求不检验）
        # 判断请求是否来自于api文档，如果是返回指定格式的结果，用于修复api文档认证成功后token显示undefined的bug
        referer = request.headers.get('referer', '')
        if referer:
            request_from_swagger = referer.endswith('docs') or settings.APP_DOCS_URL in referer
            request_from_redoc = referer.endswith('redoc') or settings.APP_REDOC_URL in referer
        else:
            request_from_swagger = False
            request_from_redoc = False

        if not settings.CAPTCHA_ENABLE:
            return True
        if (request_from_swagger or request_from_redoc) and settings.APP_ENV == 'dev':
            return True

        if not settings.CAPTCHA_LOWER_ENABLE:
            await CaptchaService.check_captcha(request, credentials.captcha_key, credentials.captcha)
        else:
            await CaptchaService.check_captcha(request, credentials.captcha_key, credentials.captcha, allow_lower=True)

    @classmethod
    async def authenticate_user(
        cls, request: Request, credentials: Union[CredentialsSchema, CustomOAuth2PasswordRequestForm]
    ):
        app_redis = request.app.state.redis
        cache_account_lock_key = RedisKeyEnum.ACCOUNT_LOCK.key.format(username=credentials.username)
        cache_pwd_err_cnt_key = RedisKeyEnum.PASSWORD_ERROR_COUNT.key.format(username=credentials.username)

        lock_account = await app_redis.get(cache_account_lock_key)
        if credentials.username == lock_account:
            tdump_log.warning('账号已锁定，请稍后再试')
            raise LoginException(message='账号已锁定，请稍后再试')

        user: User = await user_dao.get_by_username_or_phone(credentials.username)
        if not user:
            raise LoginException(message='用户不存在')

        # 如果数据库中的密码不是hashed，则将密码hash然后重新存入数据库中，目的是，密码忘了，方便直接在数据库中修改为明文的密码
        if not verify_hashed_password(user.password):
            user = await user_dao.update_user_password(user.id, generate_hash_password(user.password))

        # 如果密码不正确
        if not verify_plain_password(credentials.password, user.password):
            cache_pwd_cnt = await app_redis.get(cache_pwd_err_cnt_key)
            password_error_count = int(cache_pwd_cnt) + 1 if cache_pwd_cnt else 1
            await app_redis.set(cache_pwd_err_cnt_key, password_error_count, ex=timedelta(minutes=10))
            # 如果密码错误次数大于5，则账户锁定10分钟
            if password_error_count > 5:
                await app_redis.delete(cache_pwd_err_cnt_key)
                await app_redis.set(cache_account_lock_key, credentials.username, ex=timedelta(minutes=10))
                msg = '10分钟内密码已输错超过5次，账号已锁定，请10分钟后再试'
                # tdump_log.warning(msg)
                raise LoginException(message=msg)

            raise LoginException(message='密码错误')

        if not user.is_active:
            raise LoginException(message='用户已停用')

        # 登录成功
        await app_redis.delete(cache_pwd_err_cnt_key)
        user = await user_dao.update_last_login_info(user.id, request.client.host)
        return user

    @classmethod
    async def create_token(cls, user: User, **kwargs) -> JwtOut:
        expire = timedelta(minutes=settings.JWT_ACCESS_TOKEN_EXPIRE_MINUTES)
        payload = JwtPayload(
            sub=user.username,
            user_id=user.id,
            is_superuser=user.is_superuser,
            exp=datetime.datetime.now(datetime.UTC) + expire,
            is_refresh=False,
        )
        access_token = generate_access_token(payload=payload, **kwargs)

        refresh_expire = timedelta(minutes=settings.JWT_REFRESH_TOKEN_EXPIRE_MINUTES)
        refresh_payload = JwtPayload(
            sub=user.username,
            user_id=user.id,
            is_superuser=user.is_superuser,
            exp=datetime.datetime.now(datetime.UTC) + refresh_expire,
            is_refresh=True,
        )
        refresh_token = generate_access_token(payload=refresh_payload)

        return JwtOut(access_token=access_token, refresh_token=refresh_token, expires_in=expire.total_seconds())

    @classmethod
    async def refresh_token(cls, refresh_token: str) -> JwtOut:
        token_payload = decode_access_token(refresh_token)
        if not token_payload.is_refresh:
            raise AccessException(message='非法凭据')
        username = token_payload.sub
        user: User = await user_dao.get_by_username_or_phone(username)
        return await cls.create_token(user)


class CaptchaService:
    @classmethod
    async def check_captcha(cls, request, captcha_key: str, captcha: str, allow_lower: bool = False) -> bool:
        if not captcha:
            raise TdumpException(message='验证码不能为空')
        cache_key = RedisKeyEnum.CAPTCHA.key.format(key=captcha_key)
        captcha_value = await request.app.state.redis.get(cache_key)
        if not captcha_value:
            raise TdumpException(message='验证码过期')
        if allow_lower:
            if captcha.lower() != str(captcha_value).lower():
                raise TdumpException(message='验证码错误')
        else:
            if captcha != str(captcha_value):
                raise TdumpException(message='验证码错误')
        await request.app.state.redis.delete(cache_key)
        return True

    @classmethod
    async def get_captcha(cls, request: Request):
        if not settings.CAPTCHA_ENABLE:
            raise TdumpException(message='未启用验证码模块')

        captcha_string, captcha_b64 = await get_captcha_digits_and_ascii()
        captcha_key = get_random_character()
        # 验证码放入Redis
        cache_key = RedisKeyEnum.CAPTCHA.key.format(key=captcha_key)
        await request.app.state.redis.setex(
            cache_key, timedelta(seconds=settings.CAPTCHA_EXPIRE_SECONDS), captcha_string
        )
        return {'key': CaptchaKey(captcha_key), 'b64_image': CaptchaBase64(f'data:image/png;base64,{captcha_b64}')}
