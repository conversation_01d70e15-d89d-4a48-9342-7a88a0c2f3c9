# -*- coding: utf-8 -*-
from typing import Dict, List, <PERSON><PERSON>

from app.api.v1.params import PaginationQueryParams
from app.core.exceptions.exception import ParamsValidationException, TdumpException
from app.crud.system.menu_crud import menu_dao
from app.models import Menu
from app.schemas.system.menu_schema import MenuCreate, MenuUpdate


class MenuService:
    @classmethod
    async def get_menu_with_children(cls, menu: Menu):
        menu_dict = await menu.to_dict()
        child_menus = await menu_dao.get_all_children_menu(menu.id)
        menu_dict['children'] = [await cls.get_menu_with_children(child) for child in child_menus]
        return menu_dict

    @classmethod
    async def get_menu_list(cls, page_query: PaginationQueryParams) -> Tuple[int, List[Dict]]:
        parent_menus = await menu_dao.get_all_parent_menu()
        res_menu = [await cls.get_menu_with_children(menu) for menu in parent_menus]
        return len(res_menu), res_menu

    @classmethod
    async def get_menu_detail(cls, menu_id) -> Dict:
        menu_obj = await menu_dao.get_by_menu_id(menu_id)
        return await menu_obj.to_dict()

    @classmethod
    async def create_menu(cls, menu_in: MenuCreate) -> Dict:
        menu: Menu = await menu_dao.create(menu_in)
        return await menu.to_dict()

    @classmethod
    async def update_menu(cls, menu_id: int, menu_in: MenuUpdate) -> Dict:
        menu: Menu = await menu_dao.write(menu_id, menu_in)
        return await menu.to_dict()

    @classmethod
    async def remove_menu(cls, menu_id: int) -> None:
        children_cnt = await menu_dao.get_all_children_menu_count(menu_id)
        if children_cnt > 0:
            raise ParamsValidationException('该菜单存在子菜单，不允许删除')

        try:
            await menu_dao.remove(menu_id)
        except Exception as err:
            raise TdumpException(message='删除菜单失败，菜单可能不存在') from err
