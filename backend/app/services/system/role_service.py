# -*- coding: utf-8 -*-
from typing import Dict, List, Tuple

from app.api.v1.params import PaginationQueryParams, RoleQueryParams
from app.core.exceptions.exception import NotFoundException, ParamsValidationException, TdumpException
from app.crud.system.api_crud import api_dao
from app.crud.system.menu_crud import menu_dao
from app.crud.system.role_crud import role_dao
from app.models import Role
from app.schemas.system.role_schema import RoleCreate, RoleUpdate, RoleUpdateMenusApis


class RoleService:
    @classmethod
    async def get_role_by_id(cls, role_id: int) -> Role:
        return await role_dao.get_by_role_id(role_id)

    @classmethod
    async def get_role_list(cls, page_query: PaginationQueryParams, role_query: RoleQueryParams) -> Tuple[int, List]:
        query_dict = page_query.model_dump()
        query_dict.update(role_query.model_dump())

        total, user_objs = await role_dao.get_role_list(**query_dict)

        role_data = []
        for obj in user_objs:
            user_dict = await obj.to_dict(m2m=True, exclude_fields=['password'])
            role_data.append(user_dict)

        return total, role_data

    @classmethod
    async def get_role_detail(cls, role_id) -> Dict:
        role: Role = await role_dao.get_by_role_id(role_id)
        if not role:
            raise NotFoundException(message='未找到当前用角色信息')
        return await role.to_dict()

    @classmethod
    async def create_role(cls, role_in: RoleCreate) -> Dict:
        role: Role = await role_dao.get_by_role_name(role_in.name)
        if role:
            raise ParamsValidationException(message=f'{role_in.name}角色已经存在')

        new_role = await role_dao.create(role_in)
        return await new_role.to_dict()

    @classmethod
    async def update_role(cls, role_id: int, role_in: RoleUpdate) -> Dict:
        role = await role_dao.write(record_id=role_id, obj_in=role_in)
        return await role.to_dict()

    @classmethod
    async def remove_role(cls, role_id: int) -> None:
        try:
            await role_dao.remove(role_id)
        except Exception as err:
            raise TdumpException(message='删除角色失败，角色可能不存在') from err

    @classmethod
    async def view_role_authorized(cls, role_id) -> Dict:
        role_obj: Role = await role_dao.get_by_role_id(role_id)
        data = await role_obj.to_dict(m2m=True)
        return data

    @classmethod
    async def update_role_authorized(cls, role_id, role_in: RoleUpdateMenusApis) -> None:
        role_obj = await role_dao.get_by_role_id(role_id)
        menu_objs = await menu_dao.browse(role_in.menu_ids)
        api_objs = []
        for api_info in role_in.api_infos:
            api_obj = await api_dao.get_by_path_and_method(path=api_info.get('path'), method=api_info.get('method'))
            api_objs.append(api_obj)

        await role_dao.update_role_authorized(role=role_obj, menu_objs=menu_objs, api_objs=api_objs)
