# -*- coding: utf-8 -*-
from typing import List, <PERSON><PERSON>

from app.api.v1.params import AuditQueryParams, PaginationQueryParams
from app.crud.system.audit_log_crud import audit_log_dao


class AuditLogServices:
    @classmethod
    async def get_audit_log_list(
        cls, page_query: PaginationQueryParams, audit_log_query: AuditQueryParams
    ) -> Tuple[int, List]:
        total, audit_logs = await audit_log_dao.get_audit_list(
            audit_log_query.model_dump(), page_size=page_query.page_size, page=page_query.page
        )
        data = [await audit_log.to_dict() for audit_log in audit_logs]
        return total, data
