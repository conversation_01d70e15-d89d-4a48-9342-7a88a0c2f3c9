# -*- coding: utf-8 -*-
from typing import Dict, List, Tuple

from fastapi.routing import APIRoute

from app.api.v1.params import ApiQueryParams, PaginationQueryParams
from app.core.exceptions.exception import TdumpException
from app.core.logger import tdump_log
from app.crud.system.api_crud import api_dao
from app.models import Api
from app.schemas.system.api_schema import ApiCreate, ApiUpdate


class ApiService:
    @classmethod
    async def get_api_list(cls, page_query: PaginationQueryParams, api_query: ApiQueryParams) -> Tuple[int, List[Dict]]:
        query_dict = page_query.model_dump()
        query_dict.update(api_query.model_dump())

        total, api_objs = await api_dao.get_api_list(**query_dict)
        apis = [await api.to_dict() for api in api_objs]
        return total, apis

    @classmethod
    async def get_api_detail(cls, api_id) -> Dict:
        api: Api = await api_dao.get_by_api_id(api_id)
        return await api.to_dict()

    @classmethod
    async def create_api(cls, api_in: ApiCreate) -> Dict:
        api: Api = await api_dao.create(api_in)
        return await api.to_dict()

    @classmethod
    async def update_api(cls, api_id: int, api_in: ApiUpdate) -> Dict:
        api: Api = await api_dao.write(api_id, api_in)
        return await api.to_dict()

    @classmethod
    async def remove_api(cls, api_id: int) -> None:
        try:
            await api_dao.remove(api_id)
        except Exception as err:
            raise TdumpException(message='删除接口失败，接口可能不存在') from err

    @classmethod
    async def refresh_api(cls):
        from app import fast_app

        routes = fast_app.routes

        # 删除废弃API数据
        all_api_list = []
        for route in routes:
            # 只更新有鉴权的API
            if isinstance(route, APIRoute) and len(route.dependencies) > 0:
                all_api_list.append((list(route.methods)[0], route.path_format))
        delete_api = []
        for api in await api_dao.get_all():
            if (api.method, api.path) not in all_api_list:
                delete_api.append((api.method, api.path))
        for item in delete_api:
            method, path = item
            tdump_log.debug(f'API Deleted {method} {path}')
            await api_dao.delete_api_by_path(path=path, method=method)

        for route in routes:
            if isinstance(route, APIRoute) and len(route.dependencies) > 0:
                method = list(route.methods)[0]
                path = route.path_format
                summary = route.summary
                tags = list(route.tags)[0]
                api_obj = await api_dao.get_by_path_and_method(path=path, method=method)
                if api_obj:
                    update_data = {'method': method, 'path': path, 'summary': summary, 'tags': tags}
                    await api_obj.update_from_dict(update_data).save()
                else:
                    tdump_log.debug(f'API Created {method} {path}')
                    create_data = {'method': method, 'path': path, 'summary': summary, 'tags': tags}
                    await api_dao.create(create_data)
