# -*- coding: utf-8 -*-
from typing import Dict, List, Tuple

from app.api.v1.params import PaginationQueryParams, UserQueryParams
from app.core.config import settings
from app.core.exceptions.exception import ParamsValidationException, TdumpException
from app.core.security import generate_hash_password, verify_plain_password
from app.crud.system.dept_crud import dept_dao
from app.crud.system.role_crud import role_dao
from app.crud.system.user_crud import user_dao
from app.models import Api, Menu, Role, User
from app.schemas.system.user_schema import UpdatePassword, UserCreate, UserUpdate


class UserService:
    """
    用户
    """

    @classmethod
    async def get_current_user_info(cls, user_id: int) -> Dict:
        return await cls.get_user_info(user_id)

    @classmethod
    async def get_user_info(cls, user_id: int) -> Dict:
        user: User = await user_dao.get(id=user_id)
        data = await user.to_dict(exclude_fields=['password'])
        data['avatar'] = '/static/avatar/avatar_1.png'
        return data

    @classmethod
    async def get_user_menu(cls, user_id: int) -> List[Dict]:
        user: User = await user_dao.get(id=user_id)
        menus: list[Menu] = []
        if user.is_superuser:
            menus = await Menu.all()
        else:
            role_objs: list[Role] = await user.roles
            for role_obj in role_objs:
                menu = await role_obj.menus
                menus.extend(menu)
            menus = list(set(menus))
        parent_menus: list[Menu] = []
        for menu in menus:
            if menu.parent_id == 0:
                parent_menus.append(menu)
        res = []
        for parent_menu in parent_menus:
            parent_menu_dict = await parent_menu.to_dict()
            parent_menu_dict['children'] = []
            for menu in menus:
                if menu.parent_id == parent_menu.id:
                    parent_menu_dict['children'].append(await menu.to_dict())
            res.append(parent_menu_dict)
        return res

    @classmethod
    async def get_user_api(cls, user_id: int) -> List:
        user_obj = await user_dao.get(id=user_id)

        if user_obj.is_superuser:
            api_objs: list[Api] = await Api.all()
            apis = [api.method.lower() + api.path for api in api_objs]
            return apis

        role_objs: list[Role] = await user_obj.roles
        apis = []
        for role_obj in role_objs:
            api_objs: list[Api] = await role_obj.apis
            apis.extend([api.method.lower() + api.path for api in api_objs])
        apis = list(set(apis))
        return apis

    @classmethod
    async def update_user_password(cls, user_id: int, payload: UpdatePassword) -> User:
        # todo diaoyc 修改密码之后让旧Token失效？
        user: User = await user_dao.get(id=user_id)
        if not verify_plain_password(payload.old_password, user.password):
            raise TdumpException(message='原密码验证错误')
        hashed_pwd = generate_hash_password(payload.new_password)
        user = await user_dao.update_user_password(user.id, hashed_pwd)
        return user

    @classmethod
    async def get_user_list(cls, page_query: PaginationQueryParams, user_query: UserQueryParams) -> Tuple[int, List]:
        query_dict = page_query.model_dump()
        query_dict.update(user_query.model_dump())

        total, user_objs = await user_dao.get_user_list(**query_dict)

        data = []
        for obj in user_objs:
            user_dict = await obj.to_dict(m2m=True, exclude_fields=['password'])
            data.append(user_dict)

        for item in data:
            dept_id = item.pop('dept_id', None)
            item['dept'] = await (await dept_dao.get_by_id(dept_id)).to_dict() if dept_id else {}

        return total, data

    @classmethod
    async def update_user_roles(cls, user: User, role_ids):
        role_objs = await role_dao.browse(role_ids)
        # role_objs = [await role_dao.get_by_role_id(role_id) for role_id in role_ids]
        await user_dao.update_user_roles(user, role_objs)

    @classmethod
    async def create_user(cls, user_in: UserCreate) -> Dict:
        user = await user_dao.get_by_username(user_in.username)
        if user:
            raise ParamsValidationException(message=f'用户 {user_in.username} 已经存在')

        user_in.password = generate_hash_password(user_in.password)
        new_user = await user_dao.create(obj_in=user_in)
        await cls.update_user_roles(new_user, user_in.role_ids)
        return await new_user.to_dict(m2m=True, exclude_fields=['password'])

    @classmethod
    async def update_user(cls, user_in: UserUpdate) -> User:
        user = await user_dao.write(record_id=user_in.id, obj_in=user_in)
        await cls.update_user_roles(user, user_in.role_ids)
        return await user.to_dict(m2m=True, exclude_fields=['password'])

    @classmethod
    async def remove_user(cls, user_id: int) -> None:
        try:
            await user_dao.remove(user_id)
        except Exception as err:
            raise TdumpException(message='删除用户失败，用户可能不存在') from err

    @classmethod
    async def reset_password(cls, user_id: int) -> User:
        hashed_password = generate_hash_password(settings.DEFAULT_PASSWORD)
        user: User = await user_dao.update_user_password(user_id, hashed_password=hashed_password)
        return user

    @classmethod
    async def forget_password(cls, username_or_phone: int, password: str) -> User:
        # 获取用户
        user: User = await user_dao.get_by_username_or_phone(username_or_phone)
        if not user:
            raise TdumpException(message=f'未找到用户：{username_or_phone}')
        # 设置新密码
        hashed_password = generate_hash_password(password)
        user: User = await user_dao.update_user_password(user.id, hashed_password=hashed_password)
        return user
