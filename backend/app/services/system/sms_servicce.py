# -*- coding: utf-8 -*-
"""
用来处理手机验证码
"""

from datetime import timedelta
import json
import time
from typing import Dict, Optional

from fastapi import Request, status
import requests
from requests import ConnectionError, ConnectTimeout

from app.core.config import settings
from app.core.exceptions.exception import SmsException, TdumpException
from app.core.logger import tdump_log
from app.enums.redis_enum import RedisKeyEnum
from app.utils.common import mask_phone


class SmsService:
    """
    手机验证码服务
    """

    @classmethod
    async def get_biz_no_from_redis(cls, request: Request, phone: str) -> str:
        biz_key = RedisKeyEnum.SMS_PHONE_BZI_NO.key.format(phone=phone)
        biz_no = await request.app.state.redis.get(biz_key)
        if not biz_no:
            raise SmsException()
        return biz_no

    @classmethod
    async def get_set_biz_no_to_redis(cls, request: Request, phone: str) -> str:
        biz_key = RedisKeyEnum.SMS_PHONE_BZI_NO.key.format(phone=phone)
        biz_no = await SmsService.generate_biz_no()
        await request.app.state.redis.set(biz_key, biz_no, ex=timedelta(minutes=settings.SMS_VERIFY_EXPIRE))
        return biz_no

    @classmethod
    async def generate_biz_no(cls) -> str:
        """
        生成随机的biz 时间戳
        """
        return str(int(time.time()))

    @classmethod
    async def send_code(cls, phone: str, biz_no: str) -> dict:
        """
        发送手机验证码
        """
        data = cls._build_req_data(phone, biz_no)
        tdump_log.info(f'Send SMS Code:{mask_phone(data["phone"])}')

        resp_json: Dict = await cls._request_sms_service(settings.SMS_SEND_URL, data)

        tdump_log.info(f'短信验证码验证结果:{json.dumps(resp_json)}')
        return int(resp_json.get('code')) == status.HTTP_200_OK

    @classmethod
    async def verity_code(cls, phone: str, biz_no: str, code: str) -> bool:
        """
        验证 手机验证码
        """
        data = cls._build_req_data(phone, biz_no, code)
        # 对手机号掩码后再记录日志
        tdump_log.info(f'验证短信验证码:{mask_phone(data["phone"])}')

        resp_json: Dict = await cls._request_sms_service(settings.SMS_VERIFY_URL, data)

        tdump_log.info(f'短信验证码验证结果:{json.dumps(resp_json, ensure_ascii=False)}')
        return int(resp_json.get('code')) == status.HTTP_200_OK

    @classmethod
    def _build_req_data(cls, phone, biz_no, code: str = None):
        data = {
            'client_id': settings.SMS_CLIENT_ID,
            'client_key': settings.SMS_CLIENT_KEY,
            'phone': phone,
            'biz_no': biz_no,
        }
        if code:
            data['code'] = code
        return data

    @classmethod
    async def _request_sms_service(cls, url: str, data: dict) -> Optional[Dict]:
        try:
            response = requests.post(url, json=data, timeout=settings.SMS_REQ_TIMEOUT)
            response.raise_for_status()

            return response.json()

        except ConnectTimeout as e:
            raise TdumpException(message='请求短信服务超时失败') from e
        except ConnectionError as e:
            raise TdumpException(message='请求短信服务连接失败') from e
        except Exception as e:
            raise TdumpException(message=f'请求短信服务状态异常: {str(e)}') from e
