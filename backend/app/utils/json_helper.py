# -*- coding: utf-8 -*-
import datetime
import decimal
import json
from typing import Dict

from fastapi.datastructures import FormData


__all__ = ['dict_to_json', 'json_to_dict', 'obj_to_json', 'class_to_dict']


class CsJsonEncoder(json.JSONEncoder):
    """
    自定义JSON序列化
    """

    def default(self, o):
        # 处理FormData类型
        if isinstance(o, FormData):
            return dict(o)

        # 处理具有key和__getitem__属性的对象
        if hasattr(o, 'key') and hasattr(o, '__getitem__'):
            return dict(o)

        # 根据不同类型调用相应的处理方法
        if isinstance(o, datetime.datetime):
            return self._handle_datetime(o)
        elif isinstance(o, datetime.date):
            return self._handle_date(o)
        elif isinstance(o, datetime.time):
            return self._handle_time(o)
        elif isinstance(o, decimal.Decimal):
            return self._handle_decimal(o)
        elif isinstance(o, bytes):
            return self._handle_bytes(o)
            return self._handle_declarative_meta(o)
        elif isinstance(o, dict):
            return self._handle_dict(o)

        return json.JSONEncoder.default(self, o)

    def _handle_datetime(self, o):
        return o.strftime('%Y-%m-%d %H:%M:%S')

    def _handle_date(self, o):
        return o.strftime('%Y-%m-%d')

    def _handle_time(self, o):
        return o.isoformat()

    def _handle_decimal(self, o):
        return float(o)

    def _handle_bytes(self, o):
        return str(o, encoding='utf-8')

    def _handle_declarative_meta(self, o):
        # 如果model类型的，则可以直接序列号为JSON对象
        return self.default({i.name: getattr(o, i.name) for i in o.__table__.columns})

    def _handle_dict(self, o):
        for k in o:
            try:
                if isinstance(o[k], (datetime.datetime, datetime.date)):
                    o[k] = self.default(o[k])
                else:
                    o[k] = o[k]
            except TypeError:
                o[k] = None
        return o


# 不格式化的输出ensure_ascii==false 输出中文的时候，保持中文的输出
def dict_to_json(dict_data: Dict, ensure_ascii: bool = False, indent: int = None) -> str:
    """
    将字典转换为json字符串

    :param dict_data: 字典数据
    :param ensure_ascii: 保持ASCII 编码
    :param indent: 排版缩进
    :return:
    """
    return json.dumps(dict_data, cls=CsJsonEncoder, ensure_ascii=ensure_ascii, indent=indent)


def obj_to_json(obj, ensure_ascii: bool = False, indent: int = None):
    stu = obj.__dict__  # 将对象转成dict字典
    return json.dumps(obj=stu, cls=CsJsonEncoder, ensure_ascii=ensure_ascii, indent=indent)


def json_to_dict(json_str) -> Dict:
    dict = json.loads(s=json_str)
    return dict


def class_to_dict(obj):
    if not obj:
        return None
    is_list = obj.__class__ == [].__class__
    is_set = obj.__class__ == set().__class__

    if is_list or is_set:
        obj_arr = []
        for o in obj:
            dict = {}
            dict.update(o.__dict__)
            obj_arr.append(dict)
        return obj_arr
    else:
        dict = {}
        dict.update(obj.__dict__)
        return dict.get('__data__')
