# -*- coding: utf-8 -*-
import base64
import importlib
import inspect
from io import BytesIO
from pathlib import Path
import random
import re
import string
from typing import Tuple, get_type_hints
import uuid

# import requests
from PIL import Image, ImageDraw, ImageFont


# from app.models.base import Model


# def get_location_by_ip(ip: str) -> str:
#     location = '本地'
#     if ip == '127.0.0.1' or ip == 'localhost':
#         return location
#     try:
#         ip_result = requests.get(f'https://qifu-api.baidubce.com/ip/geo/v1/district?ip={ip}')
#         if ip_result.status_code == 200:
#             data = ip_result.json().get('data')
#             address = [
#                 data.get('continennt'),
#                 data.get('country'),
#                 data.get('prov'),
#                 data.get('city'),
#                 data.get('district')
#             ]
#             location = '-'.join(address)
#     except Exception as e:
#         location = '未知'
#     return location


async def get_captcha_digits_and_ascii(length: int = 4) -> Tuple[str, str]:
    """
    获取验证码图片
    :param length:
    :return:
    """
    total_strings = string.digits + string.ascii_lowercase + string.ascii_uppercase
    random_strings = random.sample(list(total_strings), length)
    captcha_string = ''.join(random_strings)
    captcha = await generate_captcha(captcha_string)
    captcha_bytes = captcha.getvalue()
    captcha_base64 = base64.b64encode(captcha_bytes).decode()
    return captcha_string, captcha_base64


def get_random_character() -> str:
    """
    获取随机字符
    :return: 随机字符串
    """
    return uuid.uuid4().hex


async def generate_captcha(code) -> BytesIO:
    """
    生成带有噪声和干扰的验证码图片
    https://gitee.com/senqi666/fastapi-vue-admin/blob/master/backend/app/utils/tools.py
    :return: 验证码图片流
    """
    # 创建一张随机颜色背景的图片
    background_color = (random.randint(200, 255), random.randint(200, 255), random.randint(200, 255))
    width, height = 160, 60
    image = Image.new('RGB', (width, height), color=background_color)

    # 获取一个绘图对象
    draw = ImageDraw.Draw(image)

    # 字体设置（如果需要自定义字体，请替换下面的字体路径）
    font_path = Path(__file__).parent.parent / 'resources' / 'gantians.otf'
    font = ImageFont.truetype(font_path, 42)

    # 计算验证码文本的总宽度
    total_text_width = 0
    for char in code:
        # 计算文本的宽度
        bbox = ImageDraw.Draw(Image.new('RGB', (1, 1))).textbbox((0, 0), char, font=font)
        text_width = bbox[2] - bbox[0]
        total_text_width += text_width

    # 计算每个字符的起始位置
    x_offset = (width - total_text_width) / 2
    # 计算文本的高度
    bbox = ImageDraw.Draw(Image.new('RGB', (1, 1))).textbbox((0, 0), code[0], font=font)
    text_height = bbox[3] - bbox[1]
    y_offset = (height - text_height) / 2 - draw.textbbox((0, 0), code[0], font=font)[1]

    # 绘制每个字符（单独的颜色和扭曲）
    for char in code:
        # 随机选择字体颜色
        text_color = (random.randint(0, 100), random.randint(0, 100), random.randint(0, 100))

        # 计算字符位置并稍微扭曲
        bbox = ImageDraw.Draw(Image.new('RGB', (1, 1))).textbbox((0, 0), char, font=font)
        char_width = bbox[2] - bbox[0]
        char_x = x_offset + random.uniform(-3, 3)
        char_y = y_offset + random.uniform(-5, 5)

        # 绘制字符
        draw.text((char_x, char_y), char, font=font, fill=text_color)

        # 更新下一个字符的位置
        x_offset += char_width + random.uniform(2, 8)

    # 添加少量的圆圈干扰
    for _ in range(random.randint(2, 4)):
        # 随机位置和大小
        x = random.randint(0, width)
        y = random.randint(0, height)
        radius = random.randint(5, 10)
        draw.ellipse((x - radius, y - radius, x + radius, y + radius), outline=text_color)

    # 添加少量的噪点
    for _ in range(random.randint(10, 20)):
        x = random.randint(0, width - 1)
        y = random.randint(0, height - 1)
        noise_size = random.randint(2, 4)
        noise_color = (random.randint(0, 50), random.randint(0, 50), random.randint(0, 50))
        draw.rectangle([x, y, x + noise_size, y + noise_size], fill=noise_color)

    # 返回验证码图片流
    stream = BytesIO()
    image.save(stream, format='PNG')

    return stream


def import_module(module: str):
    module_path, module_class = module.rsplit('.', 1)
    module = importlib.import_module(module_path)
    cls = getattr(module, module_class)
    return cls


def get_endwith_method(clazz, endwith='handler'):
    """
    获取类中指定的endwith结尾的方法名称
    :param clazz:
    :param endwith:
    :return:
    """
    # 获取所有以 "handler" 结尾的方法
    handler_methods = [
        (name, method)
        for name, method in inspect.getmembers(clazz, predicate=inspect.isfunction)
        if name.endswith(endwith)
    ]
    return handler_methods


def extract_param_type(method, param_name='exec'):
    """
    获取方法参数的类型
    :param method:
    :param param_name:
    :return:
    """
    # 获取方法签名
    sig = inspect.signature(method)
    # 获取类型注解（如 `exc: TokenAuthException`）
    type_hints = get_type_hints(method)
    # 返回参数名和类型
    return {
        param.name: type_hints.get(param.name, param.annotation)
        for param in sig.parameters.values()
        if param.name == param_name
    }


def phone_verifier(phone: str):
    """
    手机号验证器
    支持中国大陆手机号格式验证
    :param phone: 手机号
    :return: 验证后的手机号
    """
    if phone is None:
        return phone

        # 去除空格和横线
    phone = re.sub(r'[\s\-()]', '', phone)

    # 验证手机号格式
    if not re.match(r'^1[3-9]\d{9}$', phone):
        raise ValueError('手机号格式不正确')
    return phone


def mask_phone(phone: str) -> str:
    """
    对手机号进行掩码处理，保留前3位和后4位，中间用****代替
    """
    if len(phone) >= 11:
        return phone[:3] + '****' + phone[-4:]
    else:
        return phone
