# -*- coding: utf-8 -*-

from typing import List

from tortoise.expressions import Q

from app.core.exceptions.exception import NotFoundException
from app.crud.base import CRUDBase
from app.models.system.admin import Dept, DeptClosure
from app.schemas.system.user_schema import UserCreate, UserUpdate


class DeptDao(CRUDBase[Dept, UserCreate, UserUpdate]):
    def __init__(self):
        super().__init__(model=Dept)

    async def get_by_id(self, dept_id: int) -> Dept:
        dept_obj = await self.get(id=dept_id)
        if not dept_obj:
            raise NotFoundException(message='未找到部门信息')
        return dept_obj

    async def get_dept_by_name(self, name) -> List[Dept]:
        q = Q()
        # 获取所有未被软删除的部门
        q &= Q(is_deleted=False)
        if name:
            q &= Q(name__contains=name)
        all_depts = await self.model.filter(q).order_by('order')
        return all_depts

    async def update_dept_closure(self, obj: Dept):
        parent_depts = await DeptClosure.filter(descendant=obj.parent_id)
        for i in parent_depts:
            print(i.ancestor, i.descendant)
        dept_closure_objs: list[DeptClosure] = []
        # 插入父级关系
        for item in parent_depts:
            dept_closure_objs.append(DeptClosure(ancestor=item.ancestor, descendant=obj.id, level=item.level + 1))
        # 插入自身x
        dept_closure_objs.append(DeptClosure(ancestor=obj.id, descendant=obj.id, level=0))
        # 创建关系
        await DeptClosure.bulk_create(dept_closure_objs)

    async def update_dept_rel(self, dept_obj, parent_id: int):
        # 更新部门关系
        if dept_obj.parent_id != parent_id:
            await DeptClosure.filter(ancestor=dept_obj.id).delete()
            await DeptClosure.filter(descendant=dept_obj.id).delete()
            await self.update_dept_closure(dept_obj)

    async def update_dept(self, dept_obj, obj_in):
        # 更新部门信息
        dept_obj.update_from_dict(obj_in.model_dump(exclude_unset=True))
        await dept_obj.save()

    async def remove_dept(self, dept_id):
        # 删除部门
        obj = await self.get_by_id(dept_id)
        obj.is_deleted = True
        await obj.save()

        # 删除关系
        await DeptClosure.filter(descendant=dept_id).delete()


dept_dao = DeptDao()

__all__ = ['dept_dao']


# async def create_user(self, obj_in: UserCreate) -> User:
#     obj_in.password = get_password_hash(password=obj_in.password)
#     obj = await self.create(obj_in)
#     return obj
#
#     async def update_last_login(self, id: int) -> None:
#         user = await self.model.get(id=id)
#         user.last_login = datetime.now()
#         await user.save()
#
#
#     async def update_roles(self, user: User, role_ids: List[int]) -> None:
#         await user.roles.clear()
#         for role_id in role_ids:
#             role_obj = await role_controller.get(id=role_id)
#             await user.roles.add(role_obj)
#
#     async def reset_password(self, user_id: int):
#         user_obj = await self.get(id=user_id)
#         if user_obj.is_superuser:
#             raise HTTPException(status_code=403, detail="不允许重置超级管理员密码")
#         user_obj.password = get_password_hash(password="123456")
#         await user_obj.save()
#
#
# user_controller = UserController()
