# -*- coding: utf-8 -*-
from typing import List, Optional, Tuple, Union

from tortoise.expressions import Q

from app.core.exceptions.exception import NotFoundException
from app.crud.base import CRUDBase
from app.models import Api
from app.schemas.system.api_schema import ApiCreate, ApiUpdate


class ApiDao(CRUDBase[Api, ApiCreate, ApiUpdate]):
    def __init__(self):
        super().__init__(model=Api)

    async def get_by_api_id(self, api_id) -> Api:
        api_obj = await self.get(id=api_id)
        if not api_obj:
            raise NotFoundException(message='未找到接口信息')
        return api_obj

    async def get_by_path_and_method(self, path: str, method: str, is_first: bool = True) -> Union[Api, List[Api]]:
        if is_first:
            return await self.model.filter(path=path, method=method).first()
        else:
            return await self.model.filter(path=path, method=method)

    async def get_api_list(
        self,
        path: Optional[str] = None,
        summary: Optional[str] = None,
        tags: Optional[str] = None,
        page: int = 1,
        page_size: int = 10,
        **kwargs,
    ) -> Tuple[int, List[Api]]:
        q = Q()
        if path:
            q &= Q(path__contains=path)
        if summary:
            q &= Q(summary__contains=summary)
        if tags:
            q &= Q(tags__contains=tags)
        total, api_objs = await self.list(page=page, page_size=page_size, search=q, order=['tags', 'id'])
        return total, api_objs

    async def get_all(self) -> List[Api]:
        return await self.model.all()

    async def delete_api_by_path(self, path: str, method: str):
        await self.model.filter(path=path, method=method).delete()


api_dao = ApiDao()
__all__ = ['api_dao']
