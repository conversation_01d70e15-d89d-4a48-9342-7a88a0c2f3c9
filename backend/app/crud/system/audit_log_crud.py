# -*- coding: utf-8 -*-

from typing import Dict, List, Tuple

from tortoise.expressions import Q

from app.crud.base import CRUDBase
from app.models import AuditLog
from app.schemas.system.audit_log_schema import AuditLogCreate, AuditLogUpdate


class AuditLogtDao(CRUDBase[AuditLog, AuditLogCreate, AuditLogUpdate]):
    def __init__(self):
        super().__init__(model=AuditLog)

    async def get_by_id(self, audit_log_id: int) -> AuditLog:
        return await self.get(id=audit_log_id)

    async def get_audit_list(
        self, audit_log_query: Dict, page: int = 1, page_size: int = 10, **kwargs
    ) -> Tuple[int, List[AuditLog]]:
        q = Q()
        for k, v in audit_log_query.items():
            if v is None:
                continue
            if k in ['start_time', 'end_time']:
                continue
            if k == 'status':
                q &= Q(status=v)
                continue

            q &= Q(**{f'{k}__icontains': v})
        if audit_log_query.get('start_time') and audit_log_query.get('end_time'):
            q &= Q(created_at__range=[audit_log_query.get('start_time'), audit_log_query.get('end_time')])

        elif audit_log_query.get('start_time'):
            q &= Q(created_at__gte=audit_log_query.get('start_time'))
        elif audit_log_query.get('end_time'):
            q &= Q(created_at__lte=audit_log_query.get('end_time'))

        audit_logs = await self.model.filter(q).offset((page - 1) * page_size).limit(page_size).order_by('-created_at')
        total = await AuditLog.filter(q).count()
        return total, audit_logs


audit_log_dao = AuditLogtDao()

__all__ = ['audit_log_dao']
