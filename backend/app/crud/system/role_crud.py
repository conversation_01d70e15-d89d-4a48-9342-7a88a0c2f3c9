# -*- coding: utf-8 -*-
from typing import List, Optional, Tuple

from tortoise.expressions import Q

from app.core.exceptions.exception import NotFoundException
from app.crud.base import CRUDBase
from app.models import Api, Menu, Role
from app.schemas.system.role_schema import RoleCreate, RoleUpdate


class RoleDao(CRUDBase[Role, RoleCreate, RoleUpdate]):
    def __init__(self):
        super().__init__(model=Role)

    async def get_by_role_id(self, role_id) -> Role:
        role_obj = await self.get(id=role_id)
        if not role_obj:
            raise NotFoundException(message='未找到角色信息')
        return role_obj

    async def get_by_role_name(self, role_name) -> Role:
        return await self.model.filter(name=role_name).first()

    async def get_role_list(
        self, role_name: Optional[str] = None, page: int = 1, page_size: int = 10, **kwargs
    ) -> Tuple[int, List[Role]]:
        q = Q()
        if role_name:
            q &= Q(name__contains=role_name)
        total, role_objs = await self.list(page=page, page_size=page_size, search=q)
        return total, role_objs

    async def update_role_authorized(self, role: Role, menu_objs: List[Menu], api_objs: List[Api]) -> None:
        await role.menus.clear()
        for menu_obj in menu_objs:
            await role.menus.add(menu_obj)

        await role.apis.clear()
        for api_obj in api_objs:
            await role.apis.add(api_obj)


role_dao = RoleDao()

__all__ = ['role_dao']
