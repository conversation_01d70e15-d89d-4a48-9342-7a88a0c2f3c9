# -*- coding: utf-8 -*-


from datetime import datetime
from typing import List, Optional, Tuple

from tortoise.expressions import Q

from app.crud.base import CRUDBase
from app.models.system.admin import Role, User
from app.schemas.system.user_schema import UserCreate, UserUpdate


# from .role import role_controller


class UserDao(CRUDBase[User, UserCreate, UserUpdate]):
    def __init__(self):
        super().__init__(model=User)

    async def get_by_email(self, email: str) -> Optional[User]:
        return await self.model.filter(email=email).first()

    async def get_by_username(self, username: str) -> Optional[User]:
        return await self.model.filter(username=username).first()

    async def get_by_username_or_phone(self, username_or_phone: str) -> Optional[User]:
        return await self.model.filter(Q(username=username_or_phone) | Q(phone=username_or_phone)).first()

    async def update_user_password(self, user_id: int, hashed_password: str) -> User:
        return await self.write(record_id=user_id, obj_in={'password': hashed_password})

    async def update_last_login_info(self, user_id: int, ip: str) -> User:
        update_dict = {
            'last_login': datetime.now(),  # todo 时区问题？
            'login_ip': ip,
        }
        return await self.write(record_id=user_id, obj_in=update_dict)

    async def get_user_list(
        self,
        username: Optional[str] = None,
        email: Optional[str] = None,
        dept_id: Optional[int] = None,
        page: int = 1,
        page_size: int = 10,
        **kwargs,
    ) -> Tuple[int, List[User]]:
        # todo 应该可以优化，做成和odoo类似的domain表达式
        q = Q()
        if username:
            q &= Q(username__contains=username)
        if email:
            q &= Q(email__contains=email)
        if dept_id is not None:
            q &= Q(dept_id=dept_id)
        total, user_objs = await self.list(page=page, page_size=page_size, search=q)
        return total, user_objs

    async def update_user_roles(self, user: User, role_objs: List[Role]) -> None:
        await user.roles.clear()
        for role in role_objs:
            await user.roles.add(role)


user_dao = UserDao()

__all__ = ['user_dao']
