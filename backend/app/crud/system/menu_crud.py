# -*- coding: utf-8 -*-
from typing import List

from app.core.exceptions.exception import NotFoundException
from app.crud.base import CRUDBase
from app.models import Menu
from app.schemas.system.menu_schema import MenuCreate, MenuUpdate


class MenuDao(CRUDBase[Menu, MenuCreate, MenuUpdate]):
    def __init__(self):
        super().__init__(model=Menu)

    async def get_by_menu_id(self, menu_id) -> Menu:
        menu_obj = await self.get(id=menu_id)
        if not menu_obj:
            raise NotFoundException(message='未找到菜单信息')
        return menu_obj

    async def get_all_parent_menu(self) -> List[Menu]:
        parent = await self.model.filter(parent_id=0).order_by('order')
        return parent

    async def get_all_children_menu(self, parent_id: int) -> List[Menu]:
        children_mens = await self.model.filter(parent_id=parent_id).order_by('order')
        return children_mens

    async def get_all_children_menu_count(self, parent_id: int):
        return await self.model.filter(parent_id=parent_id).count()


menu_dao = MenuDao()
__all__ = ['menu_dao']
