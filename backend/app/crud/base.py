# -*- coding: utf-8 -*-
from typing import Any, Dict, Generic, List, Tuple, Type, TypeVar, Union

from pydantic import BaseModel
from tortoise.expressions import Q
from tortoise.models import Model

from app.core.exceptions.exception import NotFoundException


# Total = NewType("Total", int)

ModelType = TypeVar('ModelType', bound=Model)
CreateSchemaType = TypeVar('CreateSchemaType', bound=BaseModel)
UpdateSchemaType = TypeVar('UpdateSchemaType', bound=BaseModel)


class CRUDBase(Generic[ModelType, CreateSchemaType, UpdateSchemaType]):
    def __init__(self, model: Type[ModelType]):
        self.model = model

    async def get(self, **kwargs) -> ModelType:
        return await self.model.get(**kwargs)

    async def browse(self, ids: List[int]) -> List[ModelType]:
        return await self.model.filter(id__in=ids)

    # async def get(self, id: int) -> ModelType:
    #     return await self.model.get(id=id)

    async def list(self, page: int, page_size: int, search: Q = Q(), order: list = None) -> Tuple[int, List[ModelType]]:
        query = self.model.filter(search)
        record_count = await query.count()
        if order:
            records = await query.offset((page - 1) * page_size).limit(page_size).order_by(*order)
        else:
            records = await query.offset((page - 1) * page_size).limit(page_size)
        return record_count, records

    async def create(self, obj_in: Union[CreateSchemaType, Dict[str, Any]]) -> ModelType:
        if isinstance(obj_in, Dict):
            obj_dict = obj_in
        else:
            obj_dict = obj_in.model_dump()
        obj = self.model(**obj_dict)
        await obj.save()
        return obj

    async def write(self, record_id: int, obj_in: Union[UpdateSchemaType, Dict[str, Any]]) -> ModelType:
        obj_dict = obj_in if isinstance(obj_in, Dict) else obj_in.model_dump(exclude_unset=True, exclude={'id'})
        obj = await self.get(id=record_id)
        obj.update_from_dict(obj_dict)
        await obj.save()
        return obj

    async def remove(self, record_id: int) -> None:
        obj = await self.get(id=record_id)
        if not obj:
            raise NotFoundException('资源不存在，删除失败')
        await obj.delete()
